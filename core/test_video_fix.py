#!/usr/bin/env python3
"""
测试视频接收修复效果的脚本
"""
import sys
import os
import time
import logging
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QLabel
from PyQt5.QtCore import QTimer, pyqtSlot

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'core'))

from modules.broadcast_player import BroadcastPlayerModule
from modules.group_screen_monitor import GroupScreenMonitorModule

class VideoTestWindow(QMainWindow):
    """视频测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("视频接收测试")
        self.setGeometry(100, 100, 800, 600)
        
        # 创建模块实例
        self.broadcast_player = None
        self.group_monitor = None
        
        self.init_ui()
        self.init_modules()
        
    def init_ui(self):
        """初始化界面"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 状态标签
        self.status_label = QLabel("准备测试...")
        layout.addWidget(self.status_label)
        
        # 测试按钮
        self.test_broadcast_btn = QPushButton("测试广播播放器")
        self.test_broadcast_btn.clicked.connect(self.test_broadcast_player)
        layout.addWidget(self.test_broadcast_btn)
        
        self.test_monitor_btn = QPushButton("测试小组屏幕监控")
        self.test_monitor_btn.clicked.connect(self.test_group_monitor)
        layout.addWidget(self.test_monitor_btn)
        
        self.compare_btn = QPushButton("对比测试")
        self.compare_btn.clicked.connect(self.compare_modules)
        layout.addWidget(self.compare_btn)
        
    def init_modules(self):
        """初始化模块"""
        try:
            # 初始化广播播放器
            self.broadcast_player = BroadcastPlayerModule()
            if self.broadcast_player.initialize():
                self.status_label.setText("广播播放器初始化成功")
            else:
                self.status_label.setText("广播播放器初始化失败")
                
            # 初始化小组屏幕监控
            self.group_monitor = GroupScreenMonitorModule()
            if self.group_monitor.initialize():
                self.status_label.setText(self.status_label.text() + " | 小组监控初始化成功")
            else:
                self.status_label.setText(self.status_label.text() + " | 小组监控初始化失败")
                
        except Exception as e:
            self.status_label.setText(f"模块初始化错误: {e}")
            
    def test_broadcast_player(self):
        """测试广播播放器"""
        if self.broadcast_player:
            self.status_label.setText("正在测试广播播放器...")
            self.broadcast_player.show()
            
            # 打印VLC配置
            if hasattr(self.broadcast_player, 'vlc_player') and self.broadcast_player.vlc_player:
                config = self.broadcast_player.vlc_player.config
                vlc_settings = config.get('vlc_settings', {})
                print("广播播放器VLC配置:")
                print(f"  网络缓存: {vlc_settings.get('network_caching', '未设置')}ms")
                print(f"  直播缓存: {vlc_settings.get('live_caching', '未设置')}ms")
                print(f"  RTSP TCP: {vlc_settings.get('rtsp_tcp', '未设置')}")
                print(f"  硬件解码: {vlc_settings.get('hardware_decoding', '未设置')}")
        else:
            self.status_label.setText("广播播放器未初始化")
            
    def test_group_monitor(self):
        """测试小组屏幕监控"""
        if self.group_monitor:
            self.status_label.setText("正在测试小组屏幕监控...")
            self.group_monitor.show()
            
            # 打印VLC配置
            print("小组屏幕监控VLC配置:")
            print("  网络缓存: 300ms")
            print("  直播缓存: 300ms")
            print("  RTSP TCP: 启用")
        else:
            self.status_label.setText("小组屏幕监控未初始化")
            
    def compare_modules(self):
        """对比两个模块的配置"""
        print("\n=== 模块配置对比 ===")
        
        # 广播播放器配置
        if self.broadcast_player and hasattr(self.broadcast_player, 'vlc_player') and self.broadcast_player.vlc_player:
            config = self.broadcast_player.vlc_player.config
            vlc_settings = config.get('vlc_settings', {})
            print("广播播放器 (修复后):")
            print(f"  网络缓存: {vlc_settings.get('network_caching', 150)}ms")
            print(f"  直播缓存: {vlc_settings.get('live_caching', 150)}ms")
            print(f"  RTSP TCP: {vlc_settings.get('rtsp_tcp', False)}")
            print(f"  硬件解码: {vlc_settings.get('hardware_decoding', False)}")
            print(f"  快速解码: {vlc_settings.get('fast_decode', False)}")
        
        # 小组屏幕监控配置
        print("小组屏幕监控 (原始):")
        print("  网络缓存: 300ms")
        print("  直播缓存: 300ms")
        print("  RTSP TCP: 启用")
        print("  硬件解码: 未设置")
        print("  快速解码: 未设置")
        
        print("\n=== 修复说明 ===")
        print("1. 统一了缓存设置，使用配置文件中的50ms低延迟设置")
        print("2. 添加了RTSP TCP选项，提高连接稳定性")
        print("3. 启用了硬件解码和快速解码选项")
        print("4. 改进了重连机制，更智能地处理连接中断")
        print("5. 增强了状态监控，更好地检测播放状态")
        
        self.status_label.setText("配置对比完成，请查看控制台输出")

def main():
    """主函数"""
    # 设置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    app = QApplication(sys.argv)
    
    # 创建测试窗口
    window = VideoTestWindow()
    window.show()
    
    print("视频接收修复测试启动")
    print("请使用界面按钮测试不同模块的视频接收效果")
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
