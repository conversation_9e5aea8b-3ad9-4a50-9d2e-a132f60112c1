"""
主窗口
"""
import sys
import os
import logging
from PyQt5.QtWidgets import *
from PyQt5.QtGui import *
from PyQt5.QtCore import *
from modules.css_manager import MyCss
from auth_manager import AuthManager
from module_manager import <PERSON>duleManager, ModuleInfo
from typing import Dict, Optional

class ModuleButton(QPushButton):
    """模块按钮类"""
    
    def __init__(self, module_info: ModuleInfo, parent=None):
        super().__init__(module_info.display_name, parent)
        self.module_info = module_info
        self.is_active = False
        
        # 设置按钮属性
        self.setFixedSize(64, 50)
        self.setToolTip(module_info.tooltip)
        self.update_style()
    
    def update_style(self):
        """更新按钮样式"""
        if self.is_active:
            self.setStyleSheet("background-color: red; color: white; border-radius: 5px; font-weight: bold;")
        else:
            self.setStyleSheet(MyCss.butBCss)
    
    def set_active(self, active: bool):
        """设置按钮激活状态"""
        self.is_active = active
        self.update_style()

class MainWindow(QMainWindow):
    """主窗口类"""
    
    def __init__(self, auth_manager: AuthManager, module_manager: ModuleManager):
        super().__init__()
        
        self.auth_manager = auth_manager
        self.module_manager = module_manager
        self.module_manager.set_auth_status(self.auth_manager.is_authenticated(), self.auth_manager.get_user_role())
        self.module_buttons: Dict[str, ModuleButton] = {}
        self.active_modules: Dict[str, object] = {}

        # 设置资源文件的基础路径
        self.base_dir = os.path.dirname(os.path.abspath(__file__))
        
        # 确保应用程序不会因为子窗口关闭而退出
        app = QApplication.instance()
        if app:
            app.setQuitOnLastWindowClosed(False)
        
        # 获取屏幕尺寸
        screen = QApplication.primaryScreen()
        self.screen_rect = screen.availableGeometry()
        self.screen_width = self.screen_rect.width()
        self.screen_height = self.screen_rect.height()
        
        # 设置窗口属性
        self.setWindowFlags(Qt.FramelessWindowHint | Qt.Tool | Qt.WindowStaysOnTopHint)
        self.setAttribute(Qt.WA_TranslucentBackground)
        
        # 使用资源管理器获取logo路径
        from resource_manager import get_logo_path
        logo_path = get_logo_path()
        # logging.info(f'Loading logo from: {logo_path}')
        # logging.info(f'Logo file exists: {os.path.exists(logo_path)}')
        if os.path.exists(logo_path):
            self.setWindowIcon(QIcon(logo_path))
        else:
            logging.warning(f'Logo file not found: {logo_path}')
        
        # 主窗口初始位置（右侧外部，宽度0避免影响）
        self.setGeometry(self.screen_width, (self.screen_height - 600) // 2, 0, 600)
        
        # 创建UI
        self.setup_ui()
        
        # 连接信号
        self.setup_signals()
        
        # 初始化倒计时
        self.setup_timer()
        
        # 状态管理
        self.menu_visible = True
        self.ensure_button_top()
    
    def setup_ui(self):
        """设置用户界面"""
        # 导航菜单层（独立窗口）
        self.navigation_menu = QWidget()
        self.navigation_menu.setWindowFlags(Qt.FramelessWindowHint | Qt.Tool | Qt.WindowStaysOnTopHint)
        self.navigation_menu.setGeometry(self.screen_width, (self.screen_height - 600) // 2, 200, 600)
        self.navigation_menu.setStyleSheet(MyCss.mainBgcolora)
        self.navigation_menu.show()
        
        # 使用垂直布局
        self.vertical_layout = QVBoxLayout(self.navigation_menu)
        self.vertical_layout.setSpacing(2)
        self.vertical_layout.setContentsMargins(10, 5, 10, 5)
        
        # 创建各个组件
        self.create_timer_widget()
        self.create_user_info_widget()
        self.create_button_widget()
        self.create_logo_widget()
        self.create_arrow_button()
        
        # 设置动画
        self.setup_animation()
    
    def create_timer_widget(self):
        """创建倒计时组件"""
        self.timer_widget = QWidget(self.navigation_menu)
        self.timer_widget.setFixedHeight(60)
        self.timer_layout = QVBoxLayout(self.timer_widget)
        self.timer_layout.setSpacing(0)
        self.timer_layout.setContentsMargins(0, 0, 0, 0)
        
        self.timer_label = QLabel("00:00", self.timer_widget)
        self.timer_label.setAlignment(Qt.AlignCenter)
        self.timer_label.setStyleSheet("font-size: 16px; font-weight: bold; color: white;")
        self.timer_layout.addWidget(self.timer_label)
        
        self.vertical_layout.addWidget(self.timer_widget)
    
    def create_user_info_widget(self):
        """创建用户信息组件"""
        self.user_info_widget = QWidget(self.navigation_menu)
        self.user_info_widget.setFixedHeight(80)
        self.user_info_layout = QVBoxLayout(self.user_info_widget)
        self.user_info_layout.setSpacing(2)
        self.user_info_layout.setContentsMargins(5, 5, 5, 5)
        
        # 用户名标签
        self.username_label = QLabel("", self.user_info_widget)
        self.username_label.setAlignment(Qt.AlignCenter)
        self.username_label.setStyleSheet("font-size: 12px; color: white; font-weight: bold;")
        self.user_info_layout.addWidget(self.username_label)
        
        # 角色标签
        self.role_label = QLabel("", self.user_info_widget)
        self.role_label.setAlignment(Qt.AlignCenter)
        self.role_label.setStyleSheet("font-size: 10px; color: #ccc;")
        self.user_info_layout.addWidget(self.role_label)
        
        # 登录/登出按钮
        self.auth_button = QPushButton("登录", self.user_info_widget)
        self.auth_button.setFixedHeight(25)
        self.auth_button.setStyleSheet("""
            QPushButton {
                color: white;
                border: none;
                border-radius: 3px;
                font-size: 10px;
            }
            QPushButton:hover {
                color: red;
            }
        """)
        self.auth_button.clicked.connect(self.on_auth_button_clicked)
        self.user_info_layout.addWidget(self.auth_button)
        
        self.vertical_layout.addWidget(self.user_info_widget)
        
        # 更新用户信息显示
        self.update_user_info()
    
    def create_button_widget(self):
        """创建按钮组件"""
        self.button_widget = QWidget(self.navigation_menu)
        self.button_layout = QGridLayout(self.button_widget)
        self.button_layout.setSpacing(2)
        self.button_layout.setContentsMargins(0, 0, 0, 0)
        
        # 获取可用模块并创建按钮
        self.update_module_buttons()
        
        # 添加系统按钮
        self.add_system_buttons()
        
        self.vertical_layout.addWidget(self.button_widget)
    
    def update_module_buttons(self):
        """更新模块按钮"""
        # 清除现有按钮
        for button in self.module_buttons.values():
            button.setParent(None)
        self.module_buttons.clear()
        
        # 获取可用模块
        available_modules = self.module_manager.get_available_modules()
        
        # 创建模块按钮
        row, col = 0, 0
        for module_info in available_modules:
            button = ModuleButton(module_info)
            button.clicked.connect(lambda checked, name=module_info.name: self.on_module_button_clicked(name))
            
            self.module_buttons[module_info.name] = button
            self.button_layout.addWidget(button, row, col)
            
            col += 1
            if col >= 2:  # 每行2个按钮
                col = 0
                row += 1
    
    def add_system_buttons(self):
        """添加系统按钮"""
        # 计算下一个位置
        button_count = len(self.module_buttons)
        row = button_count // 2
        col = button_count % 2
        
        # 退出按钮
        exit_button = QPushButton("退出")
        exit_button.setFixedSize(64, 50)
        exit_button.setStyleSheet(MyCss.butBCss)
        exit_button.setToolTip("退出系统")
        exit_button.clicked.connect(self.on_exit_clicked)
        
        self.button_layout.addWidget(exit_button, row, col)
    
    def create_logo_widget(self):
        """创建Logo组件"""
        self.logo_widget = QWidget(self.navigation_menu)
        self.logo_widget.setFixedHeight(80)
        
        self.logo_layout = QVBoxLayout(self.logo_widget)
        self.logo_layout.setSpacing(0)
        self.logo_layout.setContentsMargins(0, 0, 0, 0)
        
        # 创建Logo标签
        self.logo_label = QLabel(self.logo_widget)
        self.logo_label.setAlignment(Qt.AlignCenter)
        
        # 加载Logo图片
        from resource_manager import get_logo_path
        logo_path = get_logo_path()
        try:
            if os.path.exists(logo_path):
                logo_pixmap = QPixmap(logo_path)
                if not logo_pixmap.isNull():
                    self.logo_label.setPixmap(logo_pixmap.scaled(60, 60, Qt.KeepAspectRatio, Qt.SmoothTransformation))
                else:
                    raise Exception("Logo pixmap is null")
            else:
                raise Exception("Logo file not found")
        except Exception as e:
            logging.error(f'Error loading logo for widget: {e}')
            # 使用文字logo作为备选
            self.logo_label.setText("智慧课堂")
            self.logo_label.setStyleSheet("color: white; font-size: 10px; font-weight: bold;")
        
        self.logo_layout.addWidget(self.logo_label)
        self.vertical_layout.addWidget(self.logo_widget)
    
    def create_arrow_button(self):
        """创建箭头按钮"""
        self.arrow_button = QPushButton("→")
        self.arrow_button.setWindowFlags(Qt.FramelessWindowHint | Qt.Tool | Qt.WindowStaysOnTopHint | Qt.X11BypassWindowManagerHint)
        self.arrow_button.setAttribute(Qt.WA_TranslucentBackground)
        self.arrow_button.setAttribute(Qt.WA_ShowWithoutActivating)
        self.arrow_button.setGeometry(self.screen_width - 35, self.screen_height // 2, 50, 50)
        self.arrow_button.setStyleSheet("""
            QPushButton {
                background: rgba(100, 100, 100, 180);
                border-radius: 25px;
                color: white;
                font: bold 18px;
            }
            QPushButton:hover {
                background: rgba(130, 130, 130, 200);
            }
        """)
        self.arrow_button.clicked.connect(self.toggle_menu)
        self.arrow_button.show()
    
    def setup_animation(self):
        """设置动画"""
        self.animation = QPropertyAnimation(self.navigation_menu, b"geometry")
        self.animation.setDuration(300)
        self.animation.setEasingCurve(QEasingCurve.OutCubic)
        self.animation.finished.connect(self.on_animation_finished)
    
    def setup_signals(self):
        """设置信号连接"""
        # 认证管理器信号
        self.auth_manager.login_success.connect(self.on_login_success)
        self.auth_manager.logout_success.connect(self.on_logout_success)
        
        # 模块管理器信号
        self.module_manager.module_loaded.connect(self.on_module_loaded)
        self.module_manager.module_failed.connect(self.on_module_failed)
    
    def setup_timer(self):
        """设置倒计时"""
        self.timer = QTimer(self)
        self.timer.timeout.connect(self.update_timer)
        self.remaining_time = 3600  # 1小时倒计时
        self.timer.start(1000)  # 每秒更新
    
    def update_user_info(self):
        """更新用户信息显示"""
        if self.auth_manager.is_authenticated():
            user = self.auth_manager.get_current_user()
            self.username_label.setText(user.get('name', user.get('username', '用户')))
            
            role = self.auth_manager.get_user_role()
            role_text = "教师" if role == "teacher" else "用户"
            self.role_label.setText(f"v 1.0.0") # 暂时在此处显示版本型号
            
            self.auth_button.setText("登出")
        else:
            self.username_label.setText("基础功能")
            self.role_label.setText("v 1.0.0") # 暂时在此处显示版本型号
            self.auth_button.setText("登录")
    
    def on_auth_button_clicked(self):
        """认证按钮点击事件"""
        if self.auth_manager.is_authenticated():
            # 登出
            self.auth_manager.logout()
        else:
            # 显示登录对话框
            from login_dialog import LoginDialog
            dialog = LoginDialog(self)
            if dialog.exec_() == QDialog.Accepted:
                result = dialog.get_result()
                if result['mode'] == 'login':
                    self.auth_manager.login_with_credentials(result['username'], result['password'])
    
    def on_module_button_clicked(self, module_name: str):
        """模块按钮点击事件"""
        button = self.module_buttons.get(module_name)
        if not button:
            return
        
        if button.is_active:
            # 关闭模块
            self.close_module(module_name)
        else:
            # 打开模块
            self.open_module(module_name)
    
    def open_module(self, module_name: str):
        """打开模块"""
        # 检查访问权限
        if not self.module_manager.can_access_module(module_name):
            QMessageBox.warning(self, "权限不足", "您没有权限访问此功能，请先登录。" )
            return
        
        # 加载模块
        instance = self.module_manager.load_module(module_name)
        if instance:
            # 获取模块的UI组件
            widget = instance.get_widget()
            if not widget:
                self.on_module_failed(module_name, "模块没有UI界面")
                return

            # 如果不是白板模块，则设置其位置
            if module_name != "whiteboard":
                self.set_module_position(widget)

            # 调用模块自身的show方法，由模块自己决定如何显示
            if hasattr(instance, 'show'):
                instance.show()
            else:
                widget.show()
            
            # 更新按钮状态
            button = self.module_buttons.get(module_name)
            if button:
                button.set_active(True)
            
            # 记录活跃模块
            self.active_modules[module_name] = instance
    
    def close_module(self, module_name: str):
        """关闭模块"""
        instance = self.active_modules.get(module_name)
        if instance:
            try:
                if hasattr(instance, 'close'):
                    instance.close()
            except Exception as e:
                print(f"关闭模块 {module_name} 时出错: {e}")
        
        # 卸载模块
        self.module_manager.unload_module(module_name)
        
        # 更新按钮状态
        button = self.module_buttons.get(module_name)
        if button:
            button.set_active(False)
        
        # 移除活跃模块记录
        if module_name in self.active_modules:
            del self.active_modules[module_name]
    
    def set_module_position(self, module):
        """设置模块窗口位置"""
        try:
            toolbar_geometry = self.navigation_menu.geometry()
            module_geometry = module.geometry()
            
            # 计算新位置（工具栏左侧）
            new_x = toolbar_geometry.x() - module_geometry.width()
            new_y = toolbar_geometry.y()
            
            # 确保窗口在屏幕内
            if new_x < 0:
                new_x = 0
            
            module.move(new_x, new_y)
        except Exception as e:
            print(f"设置模块位置时出错: {e}")
    
    def toggle_menu(self):
        """切换菜单显示/隐藏"""
        self.menu_visible = not self.menu_visible
        
        if self.menu_visible:
            # 显示菜单
            self.navigation_menu.show()
            self.animation.setStartValue(QRect(
                self.screen_width,
                self.navigation_menu.y(),
                200, 600
            ))
            self.animation.setEndValue(QRect(
                self.screen_width - 200,
                self.navigation_menu.y(),
                200, 600
            ))
            self.arrow_button.setText("→")
        else:
            # 隐藏菜单
            self.animation.setStartValue(self.navigation_menu.geometry())
            self.animation.setEndValue(QRect(
                self.screen_width,
                self.navigation_menu.y(),
                200, 600
            ))
            self.arrow_button.setText("←")
        
        self.ensure_button_top()
        self.animation.start()
    
    def on_animation_finished(self):
        """动画完成处理"""
        if not self.menu_visible:
            self.navigation_menu.hide()
        self.ensure_button_top()
    
    def ensure_button_top(self):
        """确保箭头按钮在最顶层"""
        self.arrow_button.raise_()
        if sys.platform == "win32":
            self.arrow_button.activateWindow()
        elif sys.platform == "linux":
            self.arrow_button.setAttribute(Qt.WA_X11NetWmWindowTypeDock, True)
    
    def update_timer(self):
        """更新倒计时"""
        if self.remaining_time > 0:
            minutes = self.remaining_time // 60
            seconds = self.remaining_time % 60
            self.timer_label.setText(f"{minutes:02}:{seconds:02}")
            self.remaining_time -= 1
        else:
            self.timer.stop()
            self.timer_label.setText("时间到！")
    
    def on_login_success(self, user_info: dict):
        """登录成功处理"""
        self.module_manager.set_auth_status(True, user_info.get('role', 'teacher'))
        self.update_user_info()
        self.update_module_buttons()

        # 自动加载并启动UDP发现模块
        self.auto_load_essential_modules()
    
    def on_logout_success(self):
        """登出成功处理"""
        self.update_user_info()
        self.update_module_buttons()
        
        # 关闭所有活跃模块
        for module_name in list(self.active_modules.keys()):
            self.close_module(module_name)

    def auto_load_essential_modules(self):
        """自动加载基础模块"""
        try:
            # 加载UDP发现模块（不显示界面）
            udp_discovery = self.module_manager.load_module("udp_discovery")
            if udp_discovery:
                # 启动设备发现
                if udp_discovery.start_discovery():
                    print("UDP设备发现已自动启动")
                else:
                    print("UDP设备发现启动失败")
            else:
                print("UDP发现模块加载失败")
        except Exception as e:
            print(f"自动加载基础模块失败: {e}")
    
    def on_module_loaded(self, module_name: str):
        """模块加载成功处理"""
        print(f"模块 {module_name} 加载成功")
    
    def on_module_failed(self, module_name: str, error_message: str):
        """模块加载失败处理"""
        QMessageBox.warning(self, "模块加载失败", f"无法加载模块 {module_name}:\n{error_message}")
        
        # 重置按钮状态
        button = self.module_buttons.get(module_name)
        if button:
            button.set_active(False)
    
    def on_exit_clicked(self):
        """退出按钮点击事件"""
        reply = QMessageBox.question(
            self, "退出",
            "要退出系统吗？",
            QMessageBox.Yes | QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            self.close_all_modules()
            QApplication.instance().quit()
    
    def close_all_modules(self):
        """关闭所有模块"""
        for module_name in list(self.active_modules.keys()):
            self.close_module(module_name)
    
    def closeEvent(self, event):
        """窗口关闭事件"""
        self.close_all_modules()
        if self.navigation_menu:
            self.navigation_menu.close()
        if self.arrow_button:
            self.arrow_button.close()
        event.accept()
