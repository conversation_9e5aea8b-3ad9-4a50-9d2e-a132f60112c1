#!/usr/bin/env python3
"""
重新构建应用程序，修复VLC问题
"""
import os
import sys
import shutil
import subprocess
import logging
from pathlib import Path

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_vlc_system_files():
    """检查系统VLC文件"""
    logger.info("检查系统VLC文件...")
    
    required_files = [
        '/usr/lib/x86_64-linux-gnu/libvlc.so.5.6.0',
        '/usr/lib/x86_64-linux-gnu/libvlccore.so.9.0.0',
        '/usr/lib/x86_64-linux-gnu/vlc/plugins'
    ]
    
    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
        else:
            logger.info(f"找到: {file_path}")
    
    if missing_files:
        logger.error(f"缺少系统VLC文件: {missing_files}")
        logger.error("请安装VLC: sudo apt-get install vlc libvlc-dev")
        return False
    
    return True

def clean_build_dirs():
    """清理构建目录"""
    logger.info("清理构建目录...")
    
    dirs_to_clean = ['build', 'dist', '__pycache__']
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            logger.info(f"删除目录: {dir_name}")

def run_pyinstaller():
    """运行PyInstaller"""
    logger.info("运行PyInstaller...")

    cmd = [
        'pyinstaller',
        '--clean',
        'build_app.spec'
    ]

    logger.info(f"执行命令: {' '.join(cmd)}")

    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        logger.info("PyInstaller执行成功")
        logger.info(result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"PyInstaller执行失败: {e}")
        logger.error(f"错误输出: {e.stderr}")
        return False

def post_build_fixes():
    """构建后修复"""
    logger.info("执行构建后修复...")
    
    dist_dir = 'dist'
    if not os.path.exists(dist_dir):
        logger.error("dist目录不存在")
        return False
    
    # 检查VLC文件是否正确复制
    vlc_files = [
        'libvlc.so.5.6.0',
        'libvlccore.so.9.0.0'
    ]
    
    for vlc_file in vlc_files:
        file_path = os.path.join(dist_dir, vlc_file)
        if os.path.exists(file_path):
            logger.info(f"VLC文件已复制: {vlc_file}")
        else:
            logger.warning(f"VLC文件缺失: {vlc_file}")
    
    # 检查VLC插件目录
    plugin_dir = os.path.join(dist_dir, 'vlc', 'plugins')
    if os.path.exists(plugin_dir):
        plugin_count = len([d for d in os.listdir(plugin_dir) if os.path.isdir(os.path.join(plugin_dir, d))])
        logger.info(f"VLC插件目录已复制，包含 {plugin_count} 个插件类别")
    else:
        logger.warning("VLC插件目录缺失")
    
    # 设置可执行权限
    executable_path = os.path.join(dist_dir, 'smart_classroom')
    if os.path.exists(executable_path):
        os.chmod(executable_path, 0o755)
        logger.info("设置可执行权限")
    
    return True

def test_build():
    """测试构建结果"""
    logger.info("测试构建结果...")
    
    executable_path = os.path.join('dist', 'smart_classroom')
    if not os.path.exists(executable_path):
        logger.error("可执行文件不存在")
        return False
    
    # 简单测试 - 检查是否能启动
    try:
        result = subprocess.run([executable_path, '--help'], 
                              capture_output=True, text=True, timeout=10)
        logger.info("构建测试通过")
        return True
    except subprocess.TimeoutExpired:
        logger.warning("构建测试超时，但这可能是正常的")
        return True
    except Exception as e:
        logger.warning(f"构建测试失败: {e}")
        return False

def main():
    """主函数"""
    logger.info("开始重新构建应用程序...")
    
    # 检查系统VLC文件
    if not check_vlc_system_files():
        return 1
    
    # 清理构建目录
    clean_build_dirs()
    
    # 运行PyInstaller
    if not run_pyinstaller():
        return 1
    
    # 构建后修复
    if not post_build_fixes():
        return 1
    
    # 测试构建结果
    if not test_build():
        logger.warning("构建测试未通过，但构建可能仍然成功")
    
    logger.info("应用程序重新构建完成！")
    logger.info("可执行文件位置: dist/smart_classroom")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
