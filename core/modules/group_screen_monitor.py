"""
小组屏幕看板功能模块
"""
import sys
import os
import json
import subprocess
import threading
import time
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                            QPushButton, QComboBox, QListWidget, QListWidgetItem,
                            QGroupBox, QTextEdit, QCheckBox, QSpinBox,
                            QMessageBox, QProgressBar, QFrame, QGridLayout,
                            QTableWidget, QTableWidgetItem, QHeaderView,
                            QDialog, QDialogButtonBox, QInputDialog, QScrollArea)
from PyQt5.QtCore import QTimer, pyqtSignal, Qt, QThread, pyqtSlot, QObject, QUrl, QEvent
from PyQt5.QtGui import QFont, QColor, QPalette, QIcon, QPixmap, QPainter
try:
    from modules.base_module import TeacherModule
except ImportError:
    from .base_module import TeacherModule

# 使用VLC加载器
try:
    from vlc_loader import get_vlc, test_vlc
    vlc = None  # 将在需要时动态加载
except ImportError:
    # 回退到直接导入
    import vlc
    def get_vlc():
        return vlc
    def test_vlc():
        try:
            instance = vlc.Instance(['--intf=dummy', '--quiet'])
            if instance:
                instance.release()
                return True
            return False
        except:
            return False

try:
    from modules.udp_discovery import DeviceInfo
except ImportError:
    try:
        from .udp_discovery import DeviceInfo
    except ImportError:
        # 如果无法导入，创建一个简单的DeviceInfo类
        class DeviceInfo:
            def __init__(self, device_id, device_type, device_name, ip_address, port, capabilities):
                self.device_id = device_id
                self.device_type = device_type
                self.device_name = device_name
                self.ip_address = ip_address
                self.port = port
                self.capabilities = capabilities
                self.status = "online"

class StreamInfo:
    """视频流信息类"""
    
    def __init__(self, stream_id: str, device_id: str, device_name: str, 
                 stream_url: str, resolution: str = "1920x1080"):
        self.stream_id = stream_id
        self.device_id = device_id
        self.device_name = device_name
        self.stream_url = stream_url
        self.resolution = resolution
        self.is_active = False
        self.last_update = datetime.now()
        self.viewer_count = 0
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "stream_id": self.stream_id,
            "device_id": self.device_id,
            "device_name": self.device_name,
            "stream_url": self.stream_url,
            "resolution": self.resolution,
            "is_active": self.is_active,
            "last_update": self.last_update.isoformat(),
            "viewer_count": self.viewer_count
        }

class VideoStreamWidget(QWidget):
    """视频流显示组件"""
    
    stream_clicked = pyqtSignal(str)  # stream_id
    
    def __init__(self, stream_info: StreamInfo):
        super().__init__()
        self.stream_info = stream_info
        self.vlc_instance = None
        self.vlc_player = None
        self.video_widget = None
        self.is_playing = False
        self.fullscreen_window = None  # 用于全屏显示的新窗口
        self.init_ui()
        self.init_vlc()

    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout()
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(2)

        # 设备信息标签
        self.info_label = QLabel(f"{self.stream_info.device_name}")
        self.info_label.setAlignment(Qt.AlignCenter)
        self.info_label.setStyleSheet("""
            QLabel {
                color: white;
                background-color: rgba(0, 0, 0, 0.5);
                padding: 4px;
                font-weight: bold;
            }
        """)
        
        # 视频显示区域
        self.video_widget = QWidget()
        self.video_widget.setMinimumSize(320, 180)
        self.video_widget.setStyleSheet("background-color: black; border: 1px solid #007acc;")
        
        # 使用一个容器来放置视频和标签
        video_container = QWidget()
        video_layout = QVBoxLayout(video_container)
        video_layout.setContentsMargins(0, 0, 0, 0)
        video_layout.addWidget(self.info_label)
        video_layout.addWidget(self.video_widget, 1) # 让视频区域占据更多空间
        layout.addWidget(video_container, 1)

        # 控制按钮
        control_layout = QHBoxLayout()
        self.play_btn = QPushButton("播放")
        self.fullscreen_btn = QPushButton("全屏")
        control_layout.addWidget(self.play_btn)
        control_layout.addWidget(self.fullscreen_btn)
        layout.addLayout(control_layout)
        
        # 状态标签
        self.status_label = QLabel("未连接")
        self.status_label.setAlignment(Qt.AlignCenter)
        self.status_label.setStyleSheet("color: #ccc;")
        layout.addWidget(self.status_label)
        
        self.setLayout(layout)
        
        # 连接信号
        self.play_btn.clicked.connect(self.toggle_playback)
        self.fullscreen_btn.clicked.connect(self.toggle_fullscreen)
        self.video_widget.mouseDoubleClickEvent = self.on_video_double_clicked

    def init_vlc(self):
        """初始化VLC播放器"""
        try:
            # 动态加载VLC
            global vlc
            if vlc is None:
                vlc = get_vlc()

            # 测试VLC是否可用
            if not test_vlc():
                raise Exception("VLC功能测试失败")

            vlc_options = [
                '--intf=dummy', '--no-video-title-show', '--network-caching=300',
                '--live-caching=300', '--rtsp-tcp', '--quiet'
            ]
            self.vlc_instance = vlc.Instance(vlc_options)
            self.vlc_player = self.vlc_instance.media_player_new()
            self.set_vlc_output(self.video_widget)
        except Exception as e:
            self.status_label.setText(f"VLC初始化失败: {e}")
            print(f"VLC初始化失败: {e}")
            self.vlc_instance = None
            self.vlc_player = None

    def set_vlc_output(self, widget):
        """设置VLC视频输出窗口"""
        if not self.vlc_player or not widget:
            return
        try:
            if sys.platform.startswith('linux'):
                self.vlc_player.set_xwindow(int(widget.winId()))
            elif sys.platform.startswith('win'):
                self.vlc_player.set_hwnd(int(widget.winId()))
            elif sys.platform.startswith('darwin'):
                self.vlc_player.set_nsobject(int(widget.winId()))
        except Exception as e:
            print(f"设置VLC输出失败: {e}")

    def start_stream(self):
        """开始播放流"""
        if not self.vlc_player or not self.vlc_instance:
            self.status_label.setText("VLC播放器未初始化，请检查VLC安装")
            return
        try:
            media = self.vlc_instance.media_new(self.stream_info.stream_url)
            media.add_option(":network-caching=300")
            media.add_option(":live-caching=300")
            media.add_option(":rtsp-tcp")
            self.vlc_player.set_media(media)

            if self.vlc_player.play() == 0:
                self.is_playing = True
                self.status_label.setText("连接中...")
                self.play_btn.setText("停止")
            else:
                self.status_label.setText("播放失败")
        except Exception as e:
            self.status_label.setText(f"错误: {e}")

    def stop_stream(self):
        """停止播放流"""
        if self.vlc_player and self.is_playing:
            self.vlc_player.stop()
            self.is_playing = False
            self.status_label.setText("已停止")
            self.play_btn.setText("播放")

    def toggle_playback(self):
        """切换播放状态"""
        if self.is_playing:
            self.stop_stream()
        else:
            self.start_stream()

    def toggle_fullscreen(self):
        """切换全屏模式"""
        if self.fullscreen_window:
            self._exit_fullscreen()
        else:
            self._enter_fullscreen()

    def _enter_fullscreen(self):
        """进入全屏模式"""
        if not self.vlc_player or not self.is_playing:
            self.status_label.setText("请先播放视频")
            return

        self.fullscreen_window = QWidget()
        self.fullscreen_window.setWindowTitle(f"{self.stream_info.device_name} - 全屏模式")
        self.fullscreen_window.setStyleSheet("background-color: black;")
        self.fullscreen_window.installEventFilter(self)

        # 关键步骤：停止，切换窗口，然后重新播放
        self.vlc_player.stop()
        self.set_vlc_output(self.fullscreen_window)
        self.fullscreen_window.showFullScreen()
        self.vlc_player.play()

        # 显示提示信息
        tip_label = QLabel("全屏模式：按 ESC 键退出", self.fullscreen_window)
        tip_label.setStyleSheet("""
            QLabel {
                color: white;
                background-color: rgba(0, 0, 0, 180);
                border-radius: 5px;
                padding: 10px;
                font-size: 16px;
            }
        """)
        tip_label.adjustSize()
        tip_label.move(
            (self.fullscreen_window.width() - tip_label.width()) // 2,
            20
        )
        tip_label.show()

        # 3秒后自动隐藏
        QTimer.singleShot(3000, tip_label.deleteLater)

        self.fullscreen_btn.setText("退出全屏")

    def _exit_fullscreen(self):
        """退出全屏模式"""
        if not self.fullscreen_window:
            return

        was_playing = self.is_playing

        # 关键步骤：停止，切换回原窗口，然后重新播放
        self.vlc_player.stop()
        self.set_vlc_output(self.video_widget)

        self.fullscreen_window.removeEventFilter(self)
        self.fullscreen_window.close()
        self.fullscreen_window = None

        if was_playing:
            self.vlc_player.play()

        self.fullscreen_btn.setText("全屏")

    def eventFilter(self, source, event):
        """事件过滤器，用于处理全屏窗口的事件"""
        if source == self.fullscreen_window:
            # 按下ESC键或关闭窗口时退出全屏
            if event.type() == QEvent.KeyPress and event.key() == Qt.Key_Escape:
                self._exit_fullscreen()
                return True
            elif event.type() == QEvent.Close:
                self._exit_fullscreen()
                return True
        return super().eventFilter(source, event)

    def on_video_double_clicked(self, event):
        """视频双击事件，切换全屏"""
        self.toggle_fullscreen()
        event.accept()

    def on_video_clicked(self, event):
        """视频单击事件"""
        self.stream_clicked.emit(self.stream_info.stream_id)

    def cleanup(self):
        """清理资源"""
        self._exit_fullscreen() # 确保退出全屏
        if self.vlc_player:
            self.vlc_player.stop()
            self.vlc_player.release()
            self.vlc_player = None
        if self.vlc_instance:
            self.vlc_instance.release()
            self.vlc_instance = None

class LayoutManager:
    """布局管理器"""
    
    LAYOUTS = {
        "2x2": {"rows": 2, "cols": 2, "max_streams": 4},
        "3x3": {"rows": 3, "cols": 3, "max_streams": 9},
        "4x4": {"rows": 4, "cols": 4, "max_streams": 16},
        "single": {"rows": 1, "cols": 1, "max_streams": 1}
    }
    
    @classmethod
    def get_layout_info(cls, layout_name: str) -> Dict[str, int]:
        """获取布局信息"""
        return cls.LAYOUTS.get(layout_name, cls.LAYOUTS["2x2"])
    
    @classmethod
    def get_available_layouts(cls) -> List[str]:
        """获取可用布局列表"""
        return list(cls.LAYOUTS.keys())

class GroupScreenMonitorModule(TeacherModule):
    """小组屏幕看板功能模块"""
    
    # 模块信号
    stream_added = pyqtSignal(object)      # StreamInfo
    stream_removed = pyqtSignal(str)       # stream_id
    layout_changed = pyqtSignal(str)       # layout_name
    stream_selected = pyqtSignal(str)      # stream_id
    
    def __init__(self):
        super().__init__(
            module_name="group_screen_monitor",
            display_name="小组屏幕看板",
            version="1.0.0"
        )
        
        # 流相关
        self.available_devices: Dict[str, DeviceInfo] = {}
        self.active_streams: Dict[str, StreamInfo] = {}
        self.stream_widgets: Dict[str, VideoStreamWidget] = {}
        
        # 界面
        self.monitor_window = None
        
        # 布局
        self.current_layout = "2x2"
        self.selected_stream = None
        
        # MediaMTX相关
        # 使用默认配置，避免依赖network_config模块
        self.mediamtx_url = "rtsp://localhost:8554"
        self.mediamtx_process = None
        
        # 配置
        self.config.update({
            "auto_discover_streams": True,      # 自动发现流
            "max_concurrent_streams": 9,       # 最大并发流数
            "stream_timeout": 30,              # 流超时时间（秒）
            "auto_layout": True,               # 自动布局
            "enable_audio": False,             # 启用音频
            "video_quality": "medium",         # 视频质量
            "mediamtx_port": 8554              # MediaMTX端口
        })
    
    def get_widget(self):
        return self.monitor_window

    def _initialize_module(self) -> bool:
        """初始化小组屏幕看板模块"""
        try:
            # 检查认证状态
            if not self.is_authenticated():
                self.logger.error("小组屏幕看板模块需要教师认证")
                return False
            
            # 检查MediaMTX是否可用
            if not self._check_mediamtx_available():
                self.logger.warning("MediaMTX不可用，将使用备用方案")
            
            # 创建界面
            self.monitor_window = GroupScreenMonitorWindow(self)
            
            # 获取设备发现模块，监听设备变化
            self._setup_device_discovery()
            
            # 启动流发现
            if self.get_config("auto_discover_streams", True):
                self._start_stream_discovery()
            
            self.logger.info("小组屏幕看板模块初始化成功")
            return True
            
        except Exception as e:
            self.logger.error(f"小组屏幕看板模块初始化失败: {str(e)}")
            return False
    
    def _cleanup_module(self):
        """清理小组屏幕看板模块资源"""
        try:
            # 停止所有流并清理VLC资源
            for stream_widget in self.stream_widgets.values():
                stream_widget.stop_stream()
                stream_widget.cleanup()
            
            # 停止MediaMTX
            self._stop_mediamtx()
            
            # 关闭界面
            if self.monitor_window:
                self.monitor_window.close()
                self.monitor_window = None
            
            # 清理数据
            self.available_devices.clear()
            self.active_streams.clear()
            self.stream_widgets.clear()
            
            self.logger.info("小组屏幕看板模块资源清理完成")
            
        except Exception as e:
            self.logger.error(f"清理小组屏幕看板模块资源时出错: {str(e)}")
    
    def _handle_message(self, sender: str, message_type: str, data: Dict[str, Any]):
        """处理接收到的消息"""
        if message_type == "add_stream":
            device_id = data.get("device_id")
            stream_url = data.get("stream_url")
            if device_id and stream_url:
                self.add_stream(device_id, stream_url)
        
        elif message_type == "remove_stream":
            stream_id = data.get("stream_id")
            if stream_id:
                self.remove_stream(stream_id)
        
        elif message_type == "change_layout":
            layout_name = data.get("layout_name")
            if layout_name:
                self.change_layout(layout_name)
        
        elif message_type == "select_stream":
            stream_id = data.get("stream_id")
            if stream_id:
                self.select_stream(stream_id)
        
        elif message_type == "get_monitor_status":
            self.send_monitor_status(sender)
        
        elif message_type == "show_monitor":
            self.show()
    
    def _check_mediamtx_available(self) -> bool:
        """检查MediaMTX是否可用"""
        try:
            # 检查MediaMTX RTSP端口是否可用
            import socket
            with socket.create_connection(("localhost", 8554), timeout=5):
                return True
        except Exception:
            return False
    
    def _setup_device_discovery(self):
        """设置设备发现"""
        try:
            # 获取UDP发现模块
            udp_discovery = self._get_udp_discovery_module()
            if udp_discovery:
                # 连接设备发现信号
                udp_discovery.device_discovered.connect(self.on_device_discovered)
                udp_discovery.device_updated.connect(self.on_device_updated)
                udp_discovery.device_offline.connect(self.on_device_offline)
                
                # 获取当前设备列表
                self.available_devices = udp_discovery.get_discovered_devices()
                self.logger.info(f"设备发现设置完成，当前有 {len(self.available_devices)} 个设备")
            else:
                self.logger.warning("UDP发现模块不可用")
        except Exception as e:
            self.logger.error(f"设置设备发现失败: {e}")
    
    def _get_udp_discovery_module(self):
        """获取UDP发现模块实例"""
        try:
            if hasattr(self, 'module_manager') and self.module_manager:
                return self.module_manager.get_module_instance("udp_discovery")
            return None
        except Exception as e:
            self.logger.error(f"获取UDP发现模块失败: {e}")
            return None
    
    def _start_stream_discovery(self):
        """启动流发现"""
        try:
            # 为支持屏幕广播的设备自动添加流
            for device_id, device in self.available_devices.items():
                if hasattr(device, 'capabilities') and "screen_share" in device.capabilities and device.status == "online":
                    # 使用设备IP地址构建流URL
                    stream_url = f"rtsp://{device.ip_address}:8554/desktop"
                    self.add_stream(device_id, stream_url)
        except Exception as e:
            self.logger.error(f"启动流发现失败: {e}")
    
    def _start_mediamtx(self) -> bool:
        """启动MediaMTX"""
        try:
            if self.mediamtx_process and self.mediamtx_process.poll() is None:
                return True  # 已经在运行
            
            # 检查MediaMTX是否已经在运行
            if self._check_mediamtx_available():
                self.logger.info("MediaMTX已在运行")
                return True
            
            # 启动MediaMTX进程
            cmd = ['mediamtx']
            
            # 创建临时配置文件
            config_content = f"""
logLevel: info
rtspAddress: :{self.get_config("mediamtx_port", 8554)}
rtmpAddress: :1935
hlsAddress: :8888
webrtcAddress: :8889

paths:
  desktop:
    source: publisher
"""
            
            # 使用统一的路径管理器
            from resource_manager import path_manager
            config_path = path_manager.get_temp_path('mediamtx_temp.yml')
            try:
                with open(config_path, 'w', encoding='utf-8') as f:
                    f.write(config_content)
                cmd.append(config_path)
            except Exception as config_error:
                self.logger.warning(f"创建配置文件失败: {config_error}，使用默认配置")
            
            self.mediamtx_process = subprocess.Popen(cmd, 
                                                   stdout=subprocess.PIPE, 
                                                   stderr=subprocess.PIPE)
            
            # 等待启动
            time.sleep(3)
            
            if self.mediamtx_process.poll() is None:
                self.logger.info("MediaMTX启动成功")
                return True
            else:
                self.logger.error("MediaMTX启动失败")
                return False
                
        except FileNotFoundError:
            self.logger.error("MediaMTX可执行文件未找到")
            return False
        except Exception as e:
            self.logger.error(f"启动MediaMTX失败: {str(e)}")
            return False
    
    def _stop_mediamtx(self):
        """停止MediaMTX"""
        try:
            if self.mediamtx_process and self.mediamtx_process.poll() is None:
                self.mediamtx_process.terminate()
                try:
                    self.mediamtx_process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    self.mediamtx_process.kill()
                self.logger.info("MediaMTX已停止")
            
            # 清理临时配置文件
            try:
                from resource_manager import path_manager
                config_path = path_manager.get_temp_path('mediamtx_temp.yml')
                if os.path.exists(config_path):
                    os.remove(config_path)
            except Exception:
                pass
                
        except Exception as e:
            self.logger.error(f"停止MediaMTX失败: {str(e)}")

    def show(self):
        """显示小组屏幕看板窗口"""
        if self.monitor_window:
            self.monitor_window.show()
            self.monitor_window.raise_()
            self.monitor_window.activateWindow()

    def hide(self):
        """隐藏小组屏幕看板窗口"""
        if self.monitor_window:
            self.monitor_window.hide()

    def close(self):
        """关闭小组屏幕看板模块"""
        self.cleanup()

    def add_stream(self, device_id: str, stream_url: str = None) -> Optional[str]:
        """添加视频流"""
        try:
            if device_id not in self.available_devices:
                self.logger.error(f"设备 {device_id} 不可用")
                return None

            # 检查是否已达到最大流数
            max_streams = self.get_config("max_concurrent_streams", 9)
            if len(self.active_streams) >= max_streams:
                self.logger.error("已达到最大并发流数")
                return None

            device = self.available_devices[device_id]
            stream_id = f"stream_{device_id}_{int(time.time())}"

            # 如果没有提供stream_url，使用设备IP构建
            if not stream_url:
                stream_url = f"rtsp://{device.ip_address}:8554/desktop"

            # 创建流信息
            stream_info = StreamInfo(stream_id, device_id, device.device_name, stream_url)
            self.active_streams[stream_id] = stream_info

            # 创建流显示组件
            stream_widget = VideoStreamWidget(stream_info)
            stream_widget.stream_clicked.connect(self.on_stream_clicked)
            self.stream_widgets[stream_id] = stream_widget

            # 开始播放流
            stream_widget.start_stream()
            stream_info.is_active = True

            # 发送信号
            self.stream_added.emit(stream_info)

            # 通知其他模块
            try:
                self.broadcast_message("stream_added", {
                    "stream_id": stream_id,
                    "device_id": device_id,
                    "device_name": device.device_name,
                    "stream_url": stream_url,
                    "timestamp": datetime.now().isoformat()
                })
            except Exception as broadcast_error:
                self.logger.warning(f"广播消息失败: {broadcast_error}")

            # 自动调整布局
            if self.get_config("auto_layout", True):
                self._auto_adjust_layout()

            self.logger.info(f"添加视频流: {device.device_name}")
            return stream_id

        except Exception as e:
            self.logger.error(f"添加视频流失败: {str(e)}")
            return None

    def remove_stream(self, stream_id: str):
        """移除视频流"""
        try:
            if stream_id not in self.active_streams:
                self.logger.warning(f"视频流 {stream_id} 不存在")
                return

            stream_info = self.active_streams[stream_id]

            # 停止播放并清理资源
            if stream_id in self.stream_widgets:
                widget = self.stream_widgets[stream_id]
                widget.stop_stream()
                widget.cleanup()  # 清理VLC资源
                del self.stream_widgets[stream_id]

            # 移除流信息
            del self.active_streams[stream_id]

            # 发送信号
            self.stream_removed.emit(stream_id)

            # 通知其他模块
            try:
                self.broadcast_message("stream_removed", {
                    "stream_id": stream_id,
                    "device_id": stream_info.device_id,
                    "timestamp": datetime.now().isoformat()
                })
            except Exception as broadcast_error:
                self.logger.warning(f"广播消息失败: {broadcast_error}")

            # 自动调整布局
            if self.get_config("auto_layout", True):
                self._auto_adjust_layout()

            self.logger.info(f"移除视频流: {stream_info.device_name}")

        except Exception as e:
            self.logger.error(f"移除视频流失败: {str(e)}")

    def add_stream_by_url(self, stream_url: str) -> Optional[str]:
        """通过URL直接添加视频流，用于手动添加或测试"""
        try:
            import hashlib
            # 检查是否已达到最大流数
            max_streams = self.get_config("max_concurrent_streams", 9)
            if len(self.active_streams) >= max_streams:
                self.logger.error("已达到最大并发流数")
                QMessageBox.warning(self.monitor_window, "警告", "已达到最大并发流数")
                return None

            # 从URL生成标识符
            url_hash = hashlib.md5(stream_url.encode()).hexdigest()[:8]
            stream_id = f"manual_{url_hash}_{int(time.time())}"
            device_id = f"manual_{url_hash}"
            device_name = stream_url.split('/')[-1] or f"Manual Stream {url_hash}"

            # 创建流信息
            stream_info = StreamInfo(stream_id, device_id, device_name, stream_url)
            self.active_streams[stream_id] = stream_info

            # 创建流显示组件
            stream_widget = VideoStreamWidget(stream_info)
            stream_widget.stream_clicked.connect(self.on_stream_clicked)
            self.stream_widgets[stream_id] = stream_widget

            # 开始播放流
            stream_widget.start_stream()
            stream_info.is_active = True

            # 发送信号
            self.stream_added.emit(stream_info)

            # 自动调整布局
            if self.get_config("auto_layout", True):
                self._auto_adjust_layout()

            self.logger.info(f"手动添加视频流成功: {stream_url}")
            return stream_id

        except Exception as e:
            self.logger.error(f"通过URL添加视频流失败: {str(e)}")
            return None

    def change_layout(self, layout_name: str):
        """改变布局"""
        try:
            if layout_name not in LayoutManager.get_available_layouts():
                self.logger.error(f"布局 {layout_name} 不支持")
                return

            layout_info = LayoutManager.get_layout_info(layout_name)
            max_streams = layout_info["max_streams"]

            # 检查当前流数是否超过布局限制
            if len(self.active_streams) > max_streams:
                self.logger.warning(f"当前流数({len(self.active_streams)})超过布局限制({max_streams})")

            self.current_layout = layout_name

            # 发送信号
            self.layout_changed.emit(layout_name)

            # 通知界面更新布局
            if self.monitor_window:
                self.monitor_window.update_layout()

            self.logger.info(f"布局已改变为: {layout_name}")

        except Exception as e:
            self.logger.error(f"改变布局失败: {str(e)}")

    def select_stream(self, stream_id: str):
        """选择流"""
        try:
            if stream_id not in self.active_streams:
                self.logger.warning(f"视频流 {stream_id} 不存在")
                return

            self.selected_stream = stream_id

            # 发送信号
            self.stream_selected.emit(stream_id)

            stream_info = self.active_streams[stream_id]
            self.logger.info(f"选择视频流: {stream_info.device_name}")

        except Exception as e:
            self.logger.error(f"选择视频流失败: {str(e)}")

    def _auto_adjust_layout(self):
        """根据当前流数自动调整布局"""
        try:
            stream_count = len(self.active_streams)
            new_layout = "2x2" # 默认布局

            if stream_count == 1:
                new_layout = "single"
            elif stream_count > 1 and stream_count <= 4:
                new_layout = "2x2"
            elif stream_count > 4 and stream_count <= 9:
                new_layout = "3x3"
            elif stream_count > 9:
                new_layout = "4x4"
            
            # 仅在布局变化时才调用change_layout
            if self.current_layout != new_layout:
                self.change_layout(new_layout)
            # 如果布局未变，但流数量变了，也需要强制更新界面
            elif self.monitor_window:
                self.monitor_window.update_layout()

        except Exception as e:
            self.logger.error(f"自动调整布局失败: {str(e)}")

    def on_stream_clicked(self, stream_id: str):
        """流点击事件处理"""
        self.select_stream(stream_id)

    def on_device_discovered(self, device_info: DeviceInfo):
        """设备发现事件处理"""
        self.available_devices[device_info.device_id] = device_info

        # 如果设备支持屏幕广播，自动添加流
        if (self.get_config("auto_discover_streams", True) and
            "screen_share" in device_info.capabilities and
            device_info.status == "online"):
            # 使用设备IP地址构建流URL
            stream_url = f"rtsp://{device_info.ip_address}:8554/desktop"
            self.add_stream(device_info.device_id, stream_url)

        self.logger.info(f"发现新设备: {device_info.device_name}")

    def on_device_updated(self, device_info: DeviceInfo):
        """设备更新事件处理"""
        self.available_devices[device_info.device_id] = device_info

    def on_device_offline(self, device_id: str):
        """设备离线事件处理"""
        if device_id in self.available_devices:
            del self.available_devices[device_id]

        # 移除相关的视频流
        streams_to_remove = []
        for stream_id, stream_info in self.active_streams.items():
            if stream_info.device_id == device_id:
                streams_to_remove.append(stream_id)

        for stream_id in streams_to_remove:
            self.remove_stream(stream_id)

    def get_available_devices(self) -> List[DeviceInfo]:
        """获取支持屏幕广播的设备列表"""
        return [device for device in self.available_devices.values()
                if "screen_share" in device.capabilities and device.status == "online"]

    def get_monitor_status(self) -> Dict[str, Any]:
        """获取监控状态"""
        return {
            "available_devices": len(self.available_devices),
            "active_streams": len(self.active_streams),
            "current_layout": self.current_layout,
            "selected_stream": self.selected_stream,
            "streams": [stream.to_dict() for stream in self.active_streams.values()]
        }

    def send_monitor_status(self, requester: str):
        """发送监控状态"""
        status = self.get_monitor_status()
        self.send_message(requester, "monitor_status", status)
    

class GroupScreenMonitorWindow(QWidget):
    """小组屏幕看板窗口"""

    def __init__(self, module: 'GroupScreenMonitorModule'):
        super().__init__()
        self.module = module
        self.setWindowTitle("小组屏幕看板")
        self.setGeometry(100, 100, 1280, 720)
        # 设置窗口图标，如果图标文件不存在则跳过
        try:
            icon_path = os.path.join(os.path.dirname(__file__), '..', 'assets', 'icons', 'monitor.png')
            if os.path.exists(icon_path):
                self.setWindowIcon(QIcon(icon_path))
        except Exception as e:
            pass  # 忽略图标加载错误
        self.setStyleSheet("background-color: #333;")

        self._init_ui()
        self._connect_signals()
        self.update_layout()

    def _init_ui(self):
        """初始化UI界面"""
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(5, 5, 5, 5)
        main_layout.setSpacing(5)

        # 顶部工具栏
        toolbar = QFrame()
        toolbar.setStyleSheet("background-color: #444; border-radius: 5px;")
        toolbar.setFixedHeight(50)
        toolbar_layout = QHBoxLayout(toolbar)
        
        self.layout_label = QLabel("布局模式: 自动", styleSheet="color: white; font-weight: bold;")
        toolbar_layout.addWidget(self.layout_label)

        toolbar_layout.addStretch()

        self.status_label = QLabel("活动流: 0", styleSheet="color: white;")
        toolbar_layout.addWidget(self.status_label)

        self.stop_all_button = QPushButton("全部停止")
        toolbar_layout.addWidget(self.stop_all_button)
        
        self.add_stream_button = QPushButton("手动添加流")
        toolbar_layout.addWidget(self.add_stream_button)

        main_layout.addWidget(toolbar)

        # 视频流网格区域
        self.scroll_area = QScrollArea()
        self.scroll_area.setWidgetResizable(True)
        self.scroll_area.setStyleSheet("border: none;")
        self.grid_container = QWidget()
        self.video_grid = QGridLayout(self.grid_container)
        self.video_grid.setSpacing(5)
        self.scroll_area.setWidget(self.grid_container)
        main_layout.addWidget(self.scroll_area)

    def _connect_signals(self):
        """连接信号和槽"""
        self.stop_all_button.clicked.connect(self.stop_all_streams)
        self.add_stream_button.clicked.connect(self.add_manual_stream)

        self.module.stream_added.connect(self.on_stream_changed)
        self.module.stream_removed.connect(self.on_stream_changed)
        self.module.layout_changed.connect(self.on_layout_changed)
        self.module.stream_selected.connect(self.on_stream_selected)

    @pyqtSlot()
    def update_layout(self):
        """根据当前布局和活动流更新网格"""
        # 清除旧布局
        while self.video_grid.count():
            child = self.video_grid.takeAt(0)
            if child.widget():
                child.widget().setParent(None)

        layout_info = LayoutManager.get_layout_info(self.module.current_layout)
        rows, cols = layout_info["rows"], layout_info["cols"]
        
        active_widgets = list(self.module.stream_widgets.values())
        
        for i, widget in enumerate(active_widgets):
            if i >= rows * cols:
                widget.hide()
                continue
            
            row, col = i // cols, i % cols
            self.video_grid.addWidget(widget, row, col)
            widget.show()

        self.status_label.setText(f"活动流: {len(active_widgets)}")

    @pyqtSlot()
    def stop_all_streams(self):
        """停止所有视频流"""
        for stream_id in list(self.module.active_streams.keys()):
            self.module.remove_stream(stream_id)
    
    @pyqtSlot()
    def add_manual_stream(self):
        """手动添加视频流"""
        from PyQt5.QtWidgets import QInputDialog
        
        # 弹出输入框让用户输入RTSP地址
        url, ok = QInputDialog.getText(
            self, 
            "手动添加流", 
            "请输入完整的RTSP视频流地址:", 
            text="rtsp://127.0.0.1:8554/desktop"
        )
        
        if ok and url:
            if not url.lower().startswith("rtsp://"):
                QMessageBox.warning(self, "格式错误", "请输入有效的RTSP地址，例如 rtsp://<ip>:<port>/<path>")
                return

            # 调用新的方法通过URL添加流
            stream_id = self.module.add_stream_by_url(url)
            
            if stream_id:
                QMessageBox.information(self, "成功", f"已成功添加视频流:\n{url}")
            else:
                QMessageBox.warning(self, "失败", f"添加视频流失败，请检查地址和网络连接。")

    @pyqtSlot()
    def on_stream_changed(self):
        """流添加或移除时的槽函数"""
        self.update_layout()

    @pyqtSlot(str)
    def on_layout_changed(self, layout_name: str):
        """布局改变时的槽函数"""
        self.layout_label.setText(f"布局模式: {layout_name}")
        self.update_layout()

    @pyqtSlot(str)
    def on_stream_selected(self, stream_id: str):
        """流被选中时的槽函数，可以实现放大等功能"""
        widget = self.module.stream_widgets.get(stream_id)
        if widget:
            # 示例：将选中的流切换到单屏放大模式
            if self.module.current_layout != "single":
                self.module.change_layout("single")
            
            # 将选中的widget放到第一个位置
            current_widgets = list(self.module.stream_widgets.values())
            selected_widget = self.module.stream_widgets.pop(stream_id)
            self.module.stream_widgets = {stream_id: selected_widget, **self.module.stream_widgets}
            self.update_layout()

    def closeEvent(self, event):
        """关闭窗口事件"""
        self.hide()
        event.ignore()
