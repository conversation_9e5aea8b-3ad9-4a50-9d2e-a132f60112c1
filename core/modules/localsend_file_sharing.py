"""
LocalSend 文件分享模块
基于 LocalSend v2 API 实现的文件分享功能模块
支持与官方 LocalSend 客户端兼容的文件收发功能

主要功能：
- 自动设备发现
- 文件发送和接收
- 传输进度显示
- 传输历史记录
- 多文件支持
"""

import os
import socket
import threading
import time
import queue
import platform
import signal
from http.server import HTTPServer
from typing import Dict, Any, List
from PyQt5.QtWidgets import *
from PyQt5.QtGui import *
from PyQt5.QtCore import *
from .base_module import CommonModule

# 导入组件
try:
    from .localsend_components import (
        LocalSendRequestHandler,
        FileSender,
        FileSenderSignals
    )
    from .udp_discovery import UDPDiscoveryModule, DeviceInfo
except ImportError:
    # 如果导入失败，定义占位符类
    class LocalSendRequestHandler:
        def __init__(self, module, *args, **kwargs): pass

    class FileSender:
        def __init__(self, *args, **kwargs): pass

    class FileSenderSignals(QObject):
        progress = pyqtSignal(int)
        finished = pyqtSignal(str)
        error = pyqtSignal(str)
    
    class UDPDiscoveryModule:
        def __init__(self, *args, **kwargs): pass
    
    class DeviceInfo:
        def __init__(self, *args, **kwargs): pass

# LocalSend 协议常量
LOCALSEND_PORT = 53317
REQUEST_TIMEOUT = 30

def timeout_handler(signum, frame):
    """超时信号处理器"""
    raise TimeoutError("操作超时")

def with_timeout(timeout_seconds):
    """超时装饰器"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            # 在Windows上signal.alarm不可用，使用线程替代
            if hasattr(signal, 'alarm'):
                old_handler = signal.signal(signal.SIGALRM, timeout_handler)
                signal.alarm(timeout_seconds)
                try:
                    result = func(*args, **kwargs)
                    signal.alarm(0)  # 取消alarm
                    return result
                except TimeoutError:
                    raise
                finally:
                    signal.signal(signal.SIGALRM, old_handler)
            else:
                # Windows平台使用线程超时
                import threading
                result = [None]
                exception = [None]

                def target():
                    try:
                        result[0] = func(*args, **kwargs)
                    except Exception as e:
                        exception[0] = e

                thread = threading.Thread(target=target)
                thread.daemon = True
                thread.start()
                thread.join(timeout_seconds)

                if thread.is_alive():
                    raise TimeoutError(f"操作超时 ({timeout_seconds}秒)")

                if exception[0]:
                    raise exception[0]

                return result[0]
        return wrapper
    return decorator

class LocalSendFileSharing(CommonModule):
    """LocalSend文件分享模块"""

    def __init__(self):
        super().__init__(
            module_name="localsend_file_sharing",
            display_name="文件分享",
            version="2.0.0"
        )
        self.window = None
        self.server_thread = None
        self.discovery_module = None
        self.server = None
        self.is_running = False
        self.port = LOCALSEND_PORT
        self.device_alias = f"教学系统_{platform.node()}"
        self.device_model = "Desktop"
        self.device_ip = self._get_local_ip()
        self.discovered_devices = {}
        self.download_path = os.path.join(os.path.expanduser("~"), "Downloads", "LocalSend")
        self.transfer_history = []
        
        # 确保下载目录存在
        os.makedirs(self.download_path, exist_ok=True)

    def _initialize_module(self) -> bool:
        """初始化模块"""
        try:
            self.is_running = True
            
            # 检查端口可用性
            if not self._check_port_available():
                self.logger.error(f"端口 {LOCALSEND_PORT} 已被占用")
                return False
            
            # 创建UI窗口
            self.window = LocalSendWindow(self)

            # 启动HTTP服务器
            self.server = HTTPServer(
                (self.device_ip, LOCALSEND_PORT), 
                lambda *args, **kwargs: LocalSendRequestHandler(self, *args, **kwargs)
            )
            self.server.timeout = 1
            self.server_thread = threading.Thread(target=self._run_server, daemon=True)
            self.server_thread.start()
            self.logger.info(f"LocalSend服务器已在 {self.device_ip}:{LOCALSEND_PORT} 启动")

            # 初始化UDP设备发现模块
            try:
                self.discovery_module = UDPDiscoveryModule()
                
                # 先初始化发现模块以创建本地设备信息
                if not self.discovery_module._initialize_module():
                    self.logger.error("UDP设备发现模块初始化失败")
                    return False
            except Exception as e:
                self.logger.error(f"创建UDP设备发现模块失败: {e}")
                self.discovery_module = None
                # 继续运行，但没有设备发现功能
                self.logger.warning("LocalSend将在没有设备发现功能的情况下运行")
            
            # 配置发现模块的设备信息，添加LocalSend能力
            if self.discovery_module:
                if hasattr(self.discovery_module, 'local_device') and self.discovery_module.local_device:
                    # 更新设备名称为LocalSend格式
                    self.discovery_module.local_device.device_name = self.device_alias
                    # 添加LocalSend特定的能力标识
                    if 'localsend' not in self.discovery_module.local_device.capabilities:
                        self.discovery_module.local_device.capabilities.append('localsend')
                    # 设置LocalSend端口
                    self.discovery_module.local_device.port = LOCALSEND_PORT
                    # 确保IP地址一致
                    self.discovery_module.local_device.ip_address = self.device_ip
                    
                    self.logger.info(f"LocalSend设备信息已配置: {self.discovery_module.local_device.device_name} "
                                   f"({self.discovery_module.local_device.ip_address}:{self.discovery_module.local_device.port})")
                
                # 连接信号
                self.discovery_module.device_discovered.connect(self.handle_device_discovered)
                self.discovery_module.device_updated.connect(self.handle_device_updated)
                self.discovery_module.device_offline.connect(self.handle_device_offline)
                
                # 启动设备发现
                if self.discovery_module.start_discovery():
                    self.logger.info("UDP设备发现服务已启动")
                else:
                    self.logger.warning("UDP设备发现服务启动失败")

            return True
        except Exception as e:
            self.logger.error(f"LocalSend模块初始化失败: {e}")
            return False

    def _cleanup_module(self):
        """清理资源"""
        self.logger.info("开始清理LocalSend模块")

        # 使用超时保护整个清理过程
        try:
            self._cleanup_with_timeout()
        except TimeoutError:
            self.logger.error("清理过程超时，强制设置停止状态")
            self.is_running = False
        except Exception as e:
            self.logger.error(f"清理过程异常: {e}")
            self.is_running = False
            raise

    @with_timeout(10)  # 整个清理过程最多10秒
    def _cleanup_with_timeout(self):
        """带超时保护的清理方法"""
        try:
            # self.logger.info("步骤1: 设置停止标志")
            self.is_running = False
            # self.logger.info("停止标志已设置")

            # 1. 立即关闭窗口（避免UI阻塞）
            # self.logger.info("步骤2: 开始关闭UI窗口")
            if self.window:
                try:
                    # 使用线程来关闭窗口，避免阻塞
                    import threading
                    def close_window():
                        try:
                            self.window.close()
                            self.logger.debug("UI窗口已关闭")
                        except Exception as e:
                            self.logger.warning(f"关闭窗口时出错: {e}")

                    window_thread = threading.Thread(target=close_window, daemon=True)
                    window_thread.start()
                    window_thread.join(timeout=2)  # 最多等待2秒

                    # if window_thread.is_alive():
                    #     self.logger.warning("窗口关闭超时，继续后续清理")
                    # else:
                    #     self.logger.info("UI窗口关闭完成")
                except Exception as e:
                    self.logger.warning(f"关闭窗口线程创建失败: {e}")
            else:
                self.logger.info("无UI窗口需要关闭")

            # 2. 强制停止HTTP服务器
            # self.logger.info("步骤3: 开始关闭HTTP服务器")
            if self.server:
                try:
                    # 立即关闭服务器套接字
                    # self.logger.debug("关闭服务器套接字...")
                    self.server.server_close()
                    # self.logger.debug("HTTP服务器套接字已关闭")
                except Exception as e:
                    self.logger.warning(f"关闭服务器套接字时出错: {e}")

                # 使用线程来执行可能阻塞的shutdown操作
                def shutdown_server():
                    try:
                        # self.logger.debug("尝试关闭服务器...")
                        self.server.shutdown()
                        # self.logger.debug("HTTP服务器已关闭")
                    except Exception as e:
                        self.logger.debug(f"服务器关闭失败（可能已关闭）: {e}")

                import threading
                shutdown_thread = threading.Thread(target=shutdown_server, daemon=True)
                shutdown_thread.start()
                shutdown_thread.join(timeout=1)  # 最多等待1秒

                # if shutdown_thread.is_alive():
                #     self.logger.warning("服务器关闭超时，跳过优雅关闭")
                # else:
                #     self.logger.debug("服务器关闭完成")
            else:
                self.logger.info("无HTTP服务器需要关闭")

            # 3. 停止UDP设备发现服务
            # self.logger.info("步骤4: 开始停止UDP设备发现服务")
            if self.discovery_module:
                try:
                    # self.logger.debug("调用UDP发现服务停止方法...")
                    self.discovery_module.stop_discovery()
                    # self.logger.debug("UDP设备发现服务已停止")
                except Exception as e:
                    self.logger.warning(f"停止UDP设备发现服务时出错: {e}")
            else:
                self.logger.info("无UDP设备发现服务需要停止")

            # 4. 强制停止HTTP服务器线程
            # self.logger.info("步骤5: 开始停止HTTP服务器线程")
            if self.server_thread and self.server_thread.is_alive():
                try:
                    self.logger.debug("等待HTTP服务器线程结束...")
                    # 短时间等待线程自然结束
                    self.server_thread.join(timeout=1)  # 等待1秒
                    if self.server_thread.is_alive():
                        self.logger.warning("HTTP服务器线程未能在1秒内结束")
                        # Python线程无法强制终止，只能等待
                    else:
                        self.logger.debug("HTTP服务器线程已结束")
                except Exception as e:
                    self.logger.warning(f"等待服务器线程结束时出错: {e}")
            else:
                self.logger.info("无HTTP服务器线程需要停止")

            # 5. 清理其他资源
            # self.logger.info("步骤6: 开始清理内部数据")
            try:
                self.discovered_devices.clear()
                self.transfer_history.clear()
                self.logger.debug("已清理内部数据结构")
            except Exception as e:
                self.logger.warning(f"清理内部数据时出错: {e}")

            # self.logger.info("LocalSend模块清理完成")

        except Exception as e:
            self.logger.error(f"清理过程中发生严重错误: {e}")
            import traceback
            self.logger.error(f"错误堆栈: {traceback.format_exc()}")
            # 即使出错也要确保停止标志被设置
            self.is_running = False
            raise  # 重新抛出异常以便上层处理

    def get_widget(self):
        """返回模块UI界面"""
        return self.window

    def _check_port_available(self) -> bool:
        """检查端口是否可用"""
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.bind((self.device_ip, LOCALSEND_PORT))
                return True
        except OSError:
            return False

    def _run_server(self):
        """运行HTTP服务器"""
        try:
            self.logger.debug("HTTP服务器线程开始运行")
            while self.is_running:
                try:
                    # 使用较短的超时时间，确保能快速响应停止信号
                    self.server.timeout = 0.5  # 500ms超时
                    self.server.handle_request()
                except Exception as e:
                    if self.is_running:
                        self.logger.warning(f"处理HTTP请求时出错: {e}")
                    # 如果不再运行，退出循环
                    if not self.is_running:
                        break
            self.logger.debug("HTTP服务器线程正常退出")
        except Exception as e:
            if self.is_running:
                self.logger.error(f"服务器运行错误: {e}")
            else:
                self.logger.debug(f"服务器停止时的异常（正常）: {e}")

    def _get_local_ip(self) -> str:
        """获取本地IP地址"""
        try:
            # 方法1：通过连接外部地址获取本机IP
            s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            s.connect(("*******", 80))
            ip = s.getsockname()[0]
            s.close()
            self.logger.debug(f"通过外部连接获取到本机IP: {ip}")
            return ip
        except Exception as e:
            self.logger.warning(f"通过外部连接获取IP失败: {e}")
            try:
                # 方法2：通过主机名获取IP
                ip = socket.gethostbyname(socket.gethostname())
                self.logger.debug(f"通过主机名获取到本机IP: {ip}")
                return ip
            except Exception as e2:
                self.logger.warning(f"通过主机名获取IP失败: {e2}")
                # 方法3：遍历网络接口
                try:
                    import netifaces
                    for interface in netifaces.interfaces():
                        addrs = netifaces.ifaddresses(interface)
                        if netifaces.AF_INET in addrs:
                            for addr in addrs[netifaces.AF_INET]:
                                ip = addr['addr']
                                if not ip.startswith('127.') and not ip.startswith('169.254.'):
                                    self.logger.debug(f"通过网络接口 {interface} 获取到IP: {ip}")
                                    return ip
                except ImportError:
                    self.logger.debug("netifaces模块未安装，无法遍历网络接口")
                except Exception as e3:
                    self.logger.warning(f"遍历网络接口失败: {e3}")
                
                self.logger.warning("所有IP获取方法都失败，使用127.0.0.1")
                return "127.0.0.1"

    @pyqtSlot(object)
    def handle_device_discovered(self, device_info):
        """处理发现的设备"""
        # 只处理支持LocalSend的设备
        if hasattr(device_info, 'capabilities') and 'localsend' in device_info.capabilities:
            ip = device_info.ip_address
            if ip and ip != self.device_ip:
                if ip not in self.discovered_devices:
                    self.logger.info(f"发现新LocalSend设备: {device_info.device_name} ({ip})")
                
                # 转换为LocalSend格式
                localsend_device = {
                    'alias': device_info.device_name,
                    'deviceModel': device_info.device_type,
                    'ip': ip,
                    'port': device_info.port,
                    'capabilities': device_info.capabilities
                }
                
                self.discovered_devices[ip] = localsend_device
                
                if self.window:
                    self.window.update_device_list(list(self.discovered_devices.values()))

    @pyqtSlot(object)
    def handle_device_updated(self, device_info):
        """处理设备更新"""
        if hasattr(device_info, 'capabilities') and 'localsend' in device_info.capabilities:
            ip = device_info.ip_address
            if ip and ip != self.device_ip and ip in self.discovered_devices:
                # 更新设备信息
                localsend_device = {
                    'alias': device_info.device_name,
                    'deviceModel': device_info.device_type,
                    'ip': ip,
                    'port': device_info.port,
                    'capabilities': device_info.capabilities
                }
                
                self.discovered_devices[ip] = localsend_device
                
                if self.window:
                    self.window.update_device_list(list(self.discovered_devices.values()))

    @pyqtSlot(str)
    def handle_device_offline(self, device_id):
        """处理设备离线"""
        # 从发现的设备中移除离线设备
        devices_to_remove = []
        for ip, device in self.discovered_devices.items():
            # 这里需要根据device_id找到对应的设备
            # 由于UDP发现模块使用device_id，而LocalSend使用IP，需要建立映射
            pass  # 暂时保留设备，让超时机制处理
        
        if self.window:
            self.window.update_device_list(list(self.discovered_devices.values()))

    def send_file(self, file_path: str, target_ip: str):
        """发送文件到目标设备"""
        if not os.path.exists(file_path):
            self.logger.error(f"文件不存在: {file_path}")
            return
            
        sender = FileSender(file_path, target_ip, LOCALSEND_PORT, self)
        sender.signals.progress.connect(self.window.update_progress)
        sender.signals.finished.connect(self.window.on_send_finished)
        sender.signals.error.connect(self.window.on_send_error)
        QThreadPool.globalInstance().start(sender)

    def add_transfer_record(self, record: Dict[str, Any]):
        """添加传输记录"""
        record['timestamp'] = time.time()
        self.transfer_history.append(record)

        # 保持最近100条记录
        if len(self.transfer_history) > 100:
            self.transfer_history = self.transfer_history[-100:]

    def refresh_devices(self):
        """手动刷新设备列表"""
        if not self.is_running:
            return
            
        self.logger.info("手动刷新设备列表")
        # 清空当前设备列表
        self.discovered_devices.clear()
        if self.window:
            self.window.update_device_list([])

        if self.discovery_module:
            # 重新启动发现过程
            try:
                # 停止当前发现
                self.discovery_module.stop_discovery()
                time.sleep(0.5)  # 短暂等待
                
                # 重新启动发现
                if self.discovery_module.start_discovery():
                    self.logger.info("设备发现已重新启动")
                else:
                    self.logger.warning("重新启动设备发现失败")
                    
            except Exception as e:
                self.logger.error(f"刷新设备列表时出错: {e}")
        else:
            self.logger.warning("UDP设备发现模块不可用，无法刷新设备列表")



class LocalSendWindow(QMainWindow):
    """LocalSend模块的UI窗口"""

    def __init__(self, module: LocalSendFileSharing):
        super().__init__()
        self.module = module
        self.setWindowTitle(self.module.display_name)
        self.setGeometry(150, 150, 500, 600)
        self.selected_files = []
        self.setup_ui()
        self.setup_style()

    def setup_ui(self):
        """设置UI界面"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        layout.setSpacing(10)
        layout.setContentsMargins(15, 15, 15, 15)

        # 设备信息
        info_group = QGroupBox("本机信息")
        info_layout = QVBoxLayout(info_group)
        self.info_label = QLabel(f"设备名: {self.module.device_alias}\n"
                                f"IP地址: {self.module.device_ip}\n"
                                f"端口: {self.module.port}")
        self.info_label.setStyleSheet("font-family: monospace; padding: 5px;")
        info_layout.addWidget(self.info_label)
        layout.addWidget(info_group)

        # 设备列表
        device_group = QGroupBox("可用设备")
        device_layout = QVBoxLayout(device_group)

        self.device_list = QListWidget()
        self.device_list.setMinimumHeight(120)
        self.device_list.setToolTip("双击查看设备详情")
        self.device_list.itemDoubleClicked.connect(self.show_device_info)
        device_layout.addWidget(self.device_list)

        device_btn_layout = QHBoxLayout()
        refresh_btn = QPushButton("刷新设备")
        refresh_btn.clicked.connect(self.refresh_devices)
        # device_btn_layout.addWidget(refresh_btn)

        clear_btn = QPushButton("清空列表")
        clear_btn.clicked.connect(self.clear_devices)
        # device_btn_layout.addWidget(clear_btn)

        device_layout.addLayout(device_btn_layout)
        layout.addWidget(device_group)

        # 文件选择
        file_group = QGroupBox("文件选择")
        file_layout = QVBoxLayout(file_group)

        self.file_list = QListWidget()
        self.file_list.setMinimumHeight(100)
        file_layout.addWidget(self.file_list)

        file_btn_layout = QHBoxLayout()
        select_file_btn = QPushButton("选择文件")
        select_file_btn.clicked.connect(self.select_files)
        file_btn_layout.addWidget(select_file_btn)

        clear_files_btn = QPushButton("清空文件")
        clear_files_btn.clicked.connect(self.clear_files)
        file_btn_layout.addWidget(clear_files_btn)
        file_layout.addLayout(file_btn_layout)
        layout.addWidget(file_group)

        # 发送按钮
        self.send_btn = QPushButton("发送文件")
        self.send_btn.clicked.connect(self.send_files)
        self.send_btn.setEnabled(False)
        self.send_btn.setMinimumHeight(40)
        layout.addWidget(self.send_btn)

        # 状态和进度
        status_group = QGroupBox("传输状态")
        status_layout = QVBoxLayout(status_group)
        self.status_label = QLabel("准备就绪")
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        status_layout.addWidget(self.status_label)
        status_layout.addWidget(self.progress_bar)
        layout.addWidget(status_group)

        # 下载路径
        path_group = QGroupBox("下载设置")
        path_layout = QVBoxLayout(path_group)
        path_info_layout = QHBoxLayout()
        path_info_layout.addWidget(QLabel("下载路径:"))
        self.path_label = QLabel(self.module.download_path)
        self.path_label.setWordWrap(True)
        self.path_label.setStyleSheet("background-color: #f0f0f0; padding: 5px; border: 1px solid #ccc;")
        path_info_layout.addWidget(self.path_label, 1)

        change_path_btn = QPushButton("更改")
        change_path_btn.clicked.connect(self.change_download_path)
        path_info_layout.addWidget(change_path_btn)
        path_layout.addLayout(path_info_layout)
        layout.addWidget(path_group)

    def setup_style(self):
        """设置UI样式"""
        self.setStyleSheet("""
            QMainWindow { background-color: #f5f5f5; }
            QGroupBox {
                font-weight: bold; border: 2px solid #cccccc;
                border-radius: 5px; margin-top: 1ex; padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin; left: 10px; padding: 0 5px 0 5px;
            }
            QPushButton {
                background-color: #4CAF50; color: white; border: none;
                padding: 8px 16px; border-radius: 4px; font-weight: bold;
            }
            QPushButton:hover { background-color: #45a049; }
            QPushButton:pressed { background-color: #3d8b40; }
            QPushButton:disabled { background-color: #cccccc; color: #666666; }
            QListWidget {
                border: 1px solid #ddd; border-radius: 4px; background-color: white;
            }
            QListWidget::item { padding: 5px; border-bottom: 1px solid #eee; }
            QListWidget::item:selected { background-color: #e3f2fd; }
        """)

    def select_files(self):
        """选择文件"""
        file_paths, _ = QFileDialog.getOpenFileNames(self, "选择要发送的文件", "", "所有文件 (*.*)")
        if file_paths:
            for file_path in file_paths:
                if file_path not in self.selected_files:
                    self.selected_files.append(file_path)
            self.update_file_list()

    def update_file_list(self):
        """更新文件列表"""
        self.file_list.clear()
        for file_path in self.selected_files:
            item = QListWidgetItem(f"{os.path.basename(file_path)}")
            item.setData(Qt.UserRole, file_path)
            item.setToolTip(file_path)
            self.file_list.addItem(item)
        self.send_btn.setEnabled(len(self.selected_files) > 0)

    def clear_files(self):
        """清空文件列表"""
        self.selected_files.clear()
        self.file_list.clear()
        self.send_btn.setEnabled(False)

    def send_files(self):
        """发送文件"""
        current_item = self.device_list.currentItem()
        if not self.selected_files:
            QMessageBox.warning(self, "错误", "请先选择要发送的文件")
            return
        if not current_item:
            QMessageBox.warning(self, "错误", "请先选择目标设备")
            return

        target_ip = current_item.data(Qt.UserRole)
        device_name = current_item.text()

        reply = QMessageBox.question(
            self, "确认发送",
            f"确定要发送 {len(self.selected_files)} 个文件到 {device_name} 吗？",
            QMessageBox.Yes | QMessageBox.No, QMessageBox.Yes
        )

        if reply == QMessageBox.Yes:
            self.status_label.setText(f"正在发送到 {device_name}...")
            self.progress_bar.setVisible(True)
            self.progress_bar.setValue(0)
            self.send_btn.setEnabled(False)
            self.module.send_file(self.selected_files[0], target_ip)

    def refresh_devices(self):
        """刷新设备列表"""
        self.status_label.setText("正在搜索设备...")
        # 调用模块的刷新方法，它会处理清空和多次广播
        self.module.refresh_devices()

    def clear_devices(self):
        """清空设备列表"""
        self.module.discovered_devices.clear()
        self.device_list.clear()
        self.status_label.setText("设备列表已清空")



    def show_device_info(self, item):
        """显示设备信息"""
        ip = item.data(Qt.UserRole)
        device_info = self.module.discovered_devices.get(ip, {})
        capabilities = device_info.get('capabilities', [])
        
        info_text = f"设备信息:\n名称: {device_info.get('alias', '未知')}\n"
        info_text += f"IP地址: {device_info.get('ip', '未知')}\n"
        info_text += f"端口: {device_info.get('port', '未知')}\n"
        info_text += f"设备型号: {device_info.get('deviceModel', '未知')}\n"
        info_text += f"支持功能: {', '.join(capabilities)}"
        QMessageBox.information(self, "设备信息", info_text)

    def change_download_path(self):
        """更改下载路径"""
        new_path = QFileDialog.getExistingDirectory(self, "选择下载文件夹", self.module.download_path)
        if new_path:
            self.module.download_path = new_path
            self.path_label.setText(new_path)
            os.makedirs(new_path, exist_ok=True)

    def update_device_list(self, devices: List[Dict[str, Any]]):
        """更新设备列表"""
        self.device_list.clear()
        localsend_devices = []
        
        for device in devices:
            # 只显示支持LocalSend的设备
            capabilities = device.get('capabilities', [])
            if 'localsend' in capabilities:
                alias = device.get('alias', '未知设备')
                ip = device.get('ip', '未知IP')
                device_model = device.get('deviceModel', '')

                display_text = f"{alias}"
                if device_model:
                    display_text += f" ({device_model})"
                display_text += f" - {ip}"

                item = QListWidgetItem(display_text)
                item.setData(Qt.UserRole, ip)
                item.setToolTip(f"IP: {ip}\n设备型号: {device_model}\n支持: LocalSend文件传输")
                self.device_list.addItem(item)
                localsend_devices.append(device)

        if localsend_devices:
            self.status_label.setText(f"发现 {len(localsend_devices)} 个LocalSend设备")
        else:
            self.status_label.setText("未发现支持LocalSend的设备")

    @pyqtSlot(str, dict, 'PyQt_PyObject')
    def handle_incoming_file(self, sender_ip: str, file_info: dict, response_queue: queue.Queue):
        """处理接收文件请求"""
        try:
            files = file_info.get('files', {})
            if not files:
                response_queue.put(False)
                return

            first_file_key = next(iter(files))
            file_data = files[first_file_key]
            file_name = file_data.get('fileName', '未知文件')
            file_size = file_data.get('size', 0)
            sender_info = file_info.get('info', {})
            sender_alias = sender_info.get('alias', sender_ip)

            if file_size > 1024 * 1024:
                size_str = f"{file_size / 1024 / 1024:.2f} MB"
            elif file_size > 1024:
                size_str = f"{file_size / 1024:.2f} KB"
            else:
                size_str = f"{file_size} B"

            msg_box = QMessageBox(self)
            msg_box.setWindowTitle("文件接收请求")
            msg_box.setIcon(QMessageBox.Question)
            msg_box.setText(f"收到来自 {sender_alias} ({sender_ip}) 的文件传输请求")
            msg_box.setInformativeText(f"文件名: {file_name}\n文件大小: {size_str}\n\n是否接受此文件？")
            msg_box.setStandardButtons(QMessageBox.Yes | QMessageBox.No)
            msg_box.setDefaultButton(QMessageBox.Yes)

            reply = msg_box.exec_()

            try:
                response_queue.put(reply == QMessageBox.Yes, timeout=1)
            except queue.Full:
                self.module.logger.warning("响应队列已满")

        except Exception as e:
            self.module.logger.error(f"处理接收文件请求时出错: {e}")
            try:
                response_queue.put(False, timeout=1)
            except queue.Full:
                pass

    @pyqtSlot(int)
    def update_progress(self, value):
        """更新进度条"""
        self.progress_bar.setValue(value)
        if value >= 100:
            self.progress_bar.setVisible(False)

    @pyqtSlot(str)
    def on_send_finished(self, message):
        """发送完成"""
        self.status_label.setText(message)
        self.progress_bar.setVisible(False)
        self.send_btn.setEnabled(True)
        QMessageBox.information(self, "发送成功", message)

    @pyqtSlot(str)
    def on_send_error(self, error_message):
        """发送错误"""
        self.status_label.setText(f"发送失败: {error_message}")
        self.progress_bar.setVisible(False)
        self.send_btn.setEnabled(True)
        QMessageBox.critical(self, "发送失败", f"文件发送失败:\n{error_message}")

class FileSenderSignals(QObject):
    """文件发送器信号"""
    progress = pyqtSignal(int)
    finished = pyqtSignal(str)
    error = pyqtSignal(str)
