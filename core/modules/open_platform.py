import sys
import os
import json
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QHBoxLayout
from PyQt5.QtWebEngineWidgets import QWebEngineView, QWebEnginePage
from PyQt5.QtCore import QUrl, Qt, QThread, pyqtSignal


class CustomWebEnginePage(QWebEnginePage):
    """自定义WebEnginePage以处理SSL证书错误"""
    def certificateError(self, error):
        # 这个方法会在遇到SSL证书错误时被调用
        # 对于自签名证书，错误类型通常是 CertificateAuthorityInvalid
        # 返回 True 表示接受这个证书，忽略错误并继续加载
        print(f"SSL Certificate Error: {error.errorDescription()}")
        return True


class UrlLoaderThread(QThread):
    """用于异步加载网址的线程"""
    url_loaded = pyqtSignal(str)  # 信号，用于传递加载的网址

    def __init__(self, json_path):
        super().__init__()
        self.json_path = json_path
        self.default_url = "https://localhost:5000"

    def run(self):
        """线程运行方法"""
        # 检查 JSON 文件是否存在
        if not os.path.exists(self.json_path):
            print(f"文件 {self.json_path} 不存在，使用默认URL: {self.default_url}")
            self.url_loaded.emit(self.default_url)  # 发射默认URL信号
            return

        try:
            with open(self.json_path, "r", encoding="utf-8") as f:
                data = json.load(f)
                url = data.get("url")  # 提取 url 字段
                if url:
                    self.url_loaded.emit(url)  # 发射信号，传递网址
                else:
                    print("JSON 文件中未找到有效的网址，使用默认URL!")
                    self.url_loaded.emit(self.default_url)  # 发射默认URL信号
        except Exception as e:
            print(f"读取 JSON 文件时出错: {e}，使用默认URL!")
            self.url_loaded.emit(self.default_url)  # 发射默认URL信号


class OpenPlatformApp(QMainWindow):
    def __init__(self):
        super().__init__()
        self.initUI()

    def get_widget(self):
        return self

    def initUI(self):
        # 设置窗口标题和大小
        self.setWindowTitle("资源平台")
        self.setGeometry(0, 0, 1100, 768)
        self.setWindowFlags(Qt.Tool | self.windowFlags())

        # 创建主布局
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(5, 5, 5, 5)
        main_layout.setSpacing(5)

        # 1. 创建导航栏
        nav_bar = QHBoxLayout()

        self.back_button = QPushButton("返回")
        self.reload_button = QPushButton("刷新")
        # self.close_button = QPushButton("关闭")

        nav_bar.addWidget(self.back_button)
        nav_bar.addWidget(self.reload_button)
        nav_bar.addStretch(1)
        # nav_bar.addWidget(self.close_button)

        # 2. 创建 QWebEngineView 组件
        self.browser = QWebEngineView()
        self.custom_page = CustomWebEnginePage(self.browser)
        self.browser.setPage(self.custom_page)

        # 3. 将导航栏和浏览器添加到主布局
        main_layout.addLayout(nav_bar)
        main_layout.addWidget(self.browser)

        # 创建一个容器窗口
        container = QWidget()
        container.setLayout(main_layout)
        self.setCentralWidget(container)

        # 4. 连接信号
        self.back_button.clicked.connect(self.browser.back)
        self.reload_button.clicked.connect(self.browser.reload)
        # self.close_button.clicked.connect(self.close)
        
        self.browser.urlChanged.connect(self.update_back_button_status)
        self.back_button.setEnabled(False)

        # 延迟加载网址
        self.load_url_after_show()

    def update_back_button_status(self, url):
        """根据浏览历史启用或禁用返回按钮"""
        self.back_button.setEnabled(self.browser.history().canGoBack())

    def load_url_after_show(self):
        """在窗口显示后加载网址"""
        # 从 JSON 文件中读取网址
        # current_dir = os.path.dirname(os.path.abspath(__file__))
        self.json_path = os.path.join("./database/web_urls.json")

        # 创建并启动异步加载线程
        self.url_loader_thread = UrlLoaderThread(self.json_path)
        self.url_loader_thread.url_loaded.connect(self.on_url_loaded)  # 连接信号
        self.url_loader_thread.start()

    def on_url_loaded(self, url):
        """异步加载网址完成后的回调"""
        print(f"加载网址: {url}")
        self.browser.setUrl(QUrl(url))


if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = OpenPlatformApp()
    window.show()
    sys.exit(app.exec_())
