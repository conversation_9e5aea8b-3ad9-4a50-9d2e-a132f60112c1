"""
资源管理器 - 处理打包后的资源路径问题
"""
import os
import sys
import logging
import shutil
import qtawesome as fa
from pathlib import Path

logger = logging.getLogger(__name__)

def initialize_qtawesome():
    try:
        logger.info("qtawesome initialized without font_dir option")
    except Exception as e:
        logger.error(f"An error occurred during qtawesome initialization: {e}")

def get_resource_path(relative_path):
    """获取资源文件的绝对路径，兼容打包和开发环境
    
    Args:
        relative_path: 相对于项目根目录的资源路径
        
    Returns:
        str: 资源文件的绝对路径
    """
    try:
        # PyInstaller或打包环境
        if hasattr(sys, '_MEIPASS'):
            base_path = sys._MEIPASS
            logger.debug(f"Using PyInstaller path: {base_path}")
        elif os.path.exists("/opt/smart-classroom"):
            base_path = "/opt/smart-classroom"
            logger.debug(f"Using packaged path: {base_path}")
        else:
            # 开发环境路径
            base_path = os.path.dirname(os.path.abspath(__file__))
            logger.debug(f"Using development path: {base_path}")
        
        resource_path = os.path.join(base_path, relative_path)
        
        # 检查文件是否存在
        if os.path.exists(resource_path):
            logger.debug(f"Resource found: {resource_path}")
            return resource_path
        else:
            # 尝试其他可能的路径
            alternative_paths = [
                os.path.join(os.path.dirname(base_path), relative_path),
                os.path.join(base_path, "..", relative_path),
                relative_path  # 直接使用相对路径
            ]
            
            for alt_path in alternative_paths:
                if os.path.exists(alt_path):
                    logger.debug(f"Resource found at alternative path: {alt_path}")
                    return alt_path
            
            logger.warning(f"Resource not found: {relative_path}")
            logger.warning(f"Searched paths: {resource_path}, {alternative_paths}")
            return resource_path  # 返回原路径，让调用者处理
            
    except Exception as e:
        logger.error(f"Error getting resource path for {relative_path}: {e}")
        return relative_path

def get_logo_path():
    """获取Logo图片路径"""
    return get_resource_path("assets/images/logo.png")

def get_icon_path(icon_name):
    """获取图标路径
    
    Args:
        icon_name: 图标文件名
        
    Returns:
        str: 图标文件路径
    """
    return get_resource_path(f"assets/icons/{icon_name}")

def get_image_path(image_name):
    """获取图片路径
    
    Args:
        image_name: 图片文件名
        
    Returns:
        str: 图片文件路径
    """
    return get_resource_path(f"assets/images/{image_name}")

def list_available_resources():
    """列出可用的资源文件"""
    resources = {}
    
    # 检查assets目录
    assets_path = get_resource_path("assets")
    if os.path.exists(assets_path):
        for root, dirs, files in os.walk(assets_path):
            for file in files:
                rel_path = os.path.relpath(os.path.join(root, file), assets_path)
                resources[rel_path] = os.path.join(root, file)
    
    return resources

def ensure_directory(path):
    """确保目录存在
    
    Args:
        path: 目录路径
    """
    try:
        os.makedirs(path, exist_ok=True)
        return True
    except Exception as e:
        logger.error(f"Failed to create directory {path}: {e}")
        return False

def get_save_directory(subdir=""):
    """获取保存目录路径

    Args:
        subdir: 子目录名

    Returns:
        str: 保存目录路径
    """
    # 优先使用用户文档目录
    try:
        if sys.platform == "win32":
            base_dir = os.path.join(os.path.expanduser("~"), "Documents", "SmartClassroom")
        else:
            base_dir = os.path.join(os.path.expanduser("~"), ".smart_classroom")

        if subdir:
            save_dir = os.path.join(base_dir, subdir)
        else:
            save_dir = base_dir

        ensure_directory(save_dir)
        return save_dir

    except Exception as e:
        logger.error(f"Failed to get save directory: {e}")
        # 回退到当前目录
        return os.path.join(os.getcwd(), subdir) if subdir else os.getcwd()


class PathManager:
    """统一的路径管理器，用于管理配置文件、数据文件、日志文件等的存放位置"""

    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self._app_name = "SmartClassroom"
        self._init_paths()

    def _init_paths(self):
        """初始化路径配置"""
        # 检测运行环境
        self.is_packaged = hasattr(sys, '_MEIPASS')

        if self.is_packaged:
            # PyInstaller打包环境：使用可执行文件所在目录
            exe_dir = os.path.dirname(sys.executable)

            # 在可执行文件目录创建应用数据目录
            self.app_data_dir = os.path.join(exe_dir, "data")
            self.config_dir = os.path.join(self.app_data_dir, "config")
            self.database_dir = os.path.join(self.app_data_dir, "database")
            self.logs_dir = os.path.join(self.app_data_dir, "logs")
            self.temp_dir = os.path.join(self.app_data_dir, "temp")

        else:
            # 开发环境：使用项目目录结构
            project_root = os.path.dirname(os.path.abspath(__file__))
            self.app_data_dir = project_root
            self.config_dir = os.path.join(project_root, "config")
            self.database_dir = os.path.join(project_root, "database")
            self.logs_dir = os.path.join(project_root, "logs")
            self.temp_dir = os.path.join(project_root, "temp")

        # 确保所有目录存在
        self._ensure_directories()

    def _ensure_directories(self):
        """确保所有必要的目录存在"""
        directories = [
            self.app_data_dir,
            self.config_dir,
            self.database_dir,
            self.logs_dir,
            self.temp_dir
        ]

        for directory in directories:
            try:
                os.makedirs(directory, exist_ok=True)
                self.logger.debug(f"确保目录存在: {directory}")
            except Exception as e:
                self.logger.error(f"创建目录失败 {directory}: {e}")

    def get_config_path(self, filename: str) -> str:
        """获取配置文件路径"""
        return os.path.join(self.config_dir, filename)

    def get_database_path(self, filename: str) -> str:
        """获取数据库文件路径"""
        return os.path.join(self.database_dir, filename)

    def get_log_path(self, filename: str) -> str:
        """获取日志文件路径"""
        return os.path.join(self.logs_dir, filename)

    def get_temp_path(self, filename: str) -> str:
        """获取临时文件路径"""
        return os.path.join(self.temp_dir, filename)

    def get_app_data_path(self, filename: str) -> str:
        """获取应用数据文件路径（用于运行时生成的文件）"""
        return os.path.join(self.app_data_dir, filename)

    def get_user_data_directory(self, subdir: str = "") -> str:
        """获取用户数据目录（用于用户文件保存）"""
        try:
            if sys.platform == "win32":
                base_dir = os.path.join(os.path.expanduser("~"), "Documents", self._app_name)
            else:
                base_dir = os.path.join(os.path.expanduser("~"), f".{self._app_name.lower()}")

            if subdir:
                user_dir = os.path.join(base_dir, subdir)
            else:
                user_dir = base_dir

            os.makedirs(user_dir, exist_ok=True)
            return user_dir

        except Exception as e:
            self.logger.error(f"获取用户数据目录失败: {e}")
            # 回退到应用数据目录
            fallback_dir = os.path.join(self.app_data_dir, "user_data", subdir) if subdir else os.path.join(self.app_data_dir, "user_data")
            os.makedirs(fallback_dir, exist_ok=True)
            return fallback_dir

    def migrate_old_files(self):
        """迁移旧的文件到新的目录结构"""
        try:
            # 获取当前core目录
            core_dir = os.path.dirname(os.path.abspath(__file__))

            # 需要迁移的文件映射 (旧路径 -> 新路径获取方法)
            migration_map = {
                # 运行时文件迁移到app_data目录
                os.path.join(core_dir, "auth_token.json"): self.get_app_data_path("auth_token.json"),
                os.path.join(core_dir, "broadcast_history.json"): self.get_app_data_path("broadcast_history.json"),
                os.path.join(core_dir, "mediamtx.yml"): self.get_config_path("mediamtx.yml"),
                os.path.join(core_dir, "mediamtx.yaml"): self.get_config_path("mediamtx.yaml"),
            }

            for old_path, new_path in migration_map.items():
                if os.path.exists(old_path) and not os.path.exists(new_path):
                    try:
                        # 确保目标目录存在
                        os.makedirs(os.path.dirname(new_path), exist_ok=True)
                        # 移动文件
                        shutil.move(old_path, new_path)
                        self.logger.info(f"迁移文件: {old_path} -> {new_path}")
                    except Exception as e:
                        self.logger.error(f"迁移文件失败 {old_path} -> {new_path}: {e}")

        except Exception as e:
            self.logger.error(f"文件迁移过程出错: {e}")


# 创建全局路径管理器实例
path_manager = PathManager()