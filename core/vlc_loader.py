#!/usr/bin/env python3
"""
VLC动态加载器
用于在不同环境中正确加载VLC库
"""
import os
import sys
import platform
import logging
import ctypes
from pathlib import Path

logger = logging.getLogger(__name__)

class VLCLoader:
    """VLC动态加载器"""
    
    def __init__(self):
        self.vlc_lib_loaded = False
        self.vlc_module = None
        
    def load_vlc(self):
        """动态加载VLC"""
        if self.vlc_lib_loaded:
            return self.vlc_module
            
        try:
            # 1. 设置环境变量
            self._setup_environment()
            
            # 2. 预加载必要的库文件
            self._preload_libraries()
            
            # 3. 导入VLC模块
            import vlc
            self.vlc_module = vlc
            self.vlc_lib_loaded = True
            
            logger.info("VLC模块加载成功")
            return vlc
            
        except Exception as e:
            logger.error(f"VLC模块加载失败: {e}")
            raise ImportError(f"无法加载VLC: {e}")
    
    def _setup_environment(self):
        """设置VLC环境变量"""
        system = platform.system()
        
        if hasattr(sys, '_MEIPASS'):
            # 打包环境
            base_path = sys._MEIPASS
            logger.info(f"检测到打包环境: {base_path}")
            
            if system == "Linux":
                self._setup_linux_packaged_env(base_path)
            elif system == "Windows":
                self._setup_windows_packaged_env(base_path)
                
        else:
            # 开发环境
            logger.info("检测到开发环境")
            if system == "Linux":
                self._setup_linux_dev_env()
            elif system == "Windows":
                self._setup_windows_dev_env()
    
    def _setup_linux_packaged_env(self, base_path):
        """设置Linux打包环境"""
        # VLC插件路径 - 尝试多个可能的位置
        plugin_paths = [
            os.path.join(base_path, 'vlc', 'plugins'),
            os.path.join(base_path, 'plugins'),
            '/usr/lib/x86_64-linux-gnu/vlc/plugins',  # 系统默认路径作为后备
        ]

        plugin_path_set = False
        for plugin_path in plugin_paths:
            if os.path.exists(plugin_path):
                os.environ['VLC_PLUGIN_PATH'] = plugin_path
                logger.info(f"设置VLC_PLUGIN_PATH: {plugin_path}")
                plugin_path_set = True
                break

        if not plugin_path_set:
            logger.warning("未找到VLC插件目录，VLC可能无法正常工作")

        # 库文件路径 - 确保打包目录优先
        if 'LD_LIBRARY_PATH' in os.environ:
            os.environ['LD_LIBRARY_PATH'] = f"{base_path}:{os.environ['LD_LIBRARY_PATH']}"
        else:
            os.environ['LD_LIBRARY_PATH'] = base_path

        # 设置额外的VLC环境变量
        os.environ['VLC_DATA_PATH'] = base_path
        os.environ['VLC_LIB_PATH'] = base_path

        logger.info(f"设置LD_LIBRARY_PATH: {os.environ.get('LD_LIBRARY_PATH', '')}")
        logger.info(f"设置VLC_DATA_PATH: {base_path}")
        logger.info(f"设置VLC_LIB_PATH: {base_path}")
    
    def _setup_windows_packaged_env(self, base_path):
        """设置Windows打包环境"""
        # VLC插件路径
        plugin_path = os.path.join(base_path, 'vlc', 'plugins')
        if os.path.exists(plugin_path):
            os.environ['VLC_PLUGIN_PATH'] = plugin_path
            
        # 添加到PATH
        if 'PATH' in os.environ:
            os.environ['PATH'] = f"{base_path};{os.environ['PATH']}"
        else:
            os.environ['PATH'] = base_path
    
    def _setup_linux_dev_env(self):
        """设置Linux开发环境"""
        # 通常不需要特殊设置，使用系统默认
        pass
    
    def _setup_windows_dev_env(self):
        """设置Windows开发环境"""
        # 尝试找到VLC安装目录
        possible_paths = [
            r"C:\Program Files\VideoLAN\VLC",
            r"C:\Program Files (x86)\VideoLAN\VLC",
        ]
        
        for path in possible_paths:
            if os.path.exists(path):
                if 'PATH' in os.environ:
                    os.environ['PATH'] = f"{path};{os.environ['PATH']}"
                else:
                    os.environ['PATH'] = path
                break
    
    def _preload_libraries(self):
        """预加载必要的库文件"""
        system = platform.system()
        
        if system == "Linux":
            self._preload_linux_libraries()
        elif system == "Windows":
            self._preload_windows_libraries()
    
    def _preload_linux_libraries(self):
        """预加载Linux库文件"""
        if hasattr(sys, '_MEIPASS'):
            # 在打包环境中尝试预加载库文件
            base_path = sys._MEIPASS
            logger.info(f"尝试从打包目录预加载VLC库: {base_path}")

            # 列出打包目录中的所有文件
            try:
                files = os.listdir(base_path)
                vlc_files = [f for f in files if 'vlc' in f.lower()]
                logger.info(f"找到的VLC相关文件: {vlc_files}")
            except Exception as e:
                logger.error(f"无法列出打包目录文件: {e}")

            lib_files = [
                'libvlccore.so.9.0.0',
                'libvlccore.so.9',
                'libvlc.so.5.6.0',
                'libvlc.so.5',
                'libvlccore.so',
                'libvlc.so'
            ]

            loaded_count = 0
            for lib_file in lib_files:
                lib_path = os.path.join(base_path, lib_file)
                if os.path.exists(lib_path):
                    try:
                        lib = ctypes.CDLL(lib_path)
                        logger.info(f"预加载库文件成功: {lib_file}")
                        loaded_count += 1
                    except Exception as e:
                        logger.warning(f"预加载库文件失败 {lib_file}: {e}")
                else:
                    logger.debug(f"库文件不存在: {lib_path}")

            if loaded_count == 0:
                logger.warning("没有成功预加载任何VLC库文件")
            else:
                logger.info(f"成功预加载了 {loaded_count} 个VLC库文件")
    
    def _preload_windows_libraries(self):
        """预加载Windows库文件"""
        if hasattr(sys, '_MEIPASS'):
            # 在打包环境中尝试预加载库文件
            base_path = sys._MEIPASS
            lib_files = [
                'libvlc.dll',
                'libvlccore.dll'
            ]
            
            for lib_file in lib_files:
                lib_path = os.path.join(base_path, lib_file)
                if os.path.exists(lib_path):
                    try:
                        ctypes.CDLL(lib_path)
                        logger.info(f"预加载库文件成功: {lib_file}")
                    except Exception as e:
                        logger.warning(f"预加载库文件失败 {lib_file}: {e}")
    
    def test_vlc_functionality(self):
        """测试VLC功能"""
        if not self.vlc_lib_loaded:
            logger.error("VLC模块未加载")
            return False

        try:
            # 在打包环境中使用更兼容的选项
            if hasattr(sys, '_MEIPASS'):
                vlc_options = [
                    '--intf=dummy',
                    '--quiet',
                    '--no-video-title-show',
                    '--no-audio',
                    '--no-spu',
                    '--no-osd',
                    '--no-stats',
                    '--no-media-library',
                    '--no-playlist-enqueue',
                    '--no-interact',
                    '--no-plugins-cache',
                    '--reset-config',
                    '--no-xlib'  # 避免X11相关问题
                ]

                # 如果找到了插件路径，添加插件路径选项
                plugin_path = os.environ.get('VLC_PLUGIN_PATH')
                if plugin_path and os.path.exists(plugin_path):
                    vlc_options.append(f'--plugin-path={plugin_path}')
                    logger.info(f"使用插件路径: {plugin_path}")
            else:
                # 开发环境使用简单选项
                vlc_options = [
                    '--intf=dummy',
                    '--quiet',
                    '--no-video-title-show',
                    '--no-audio',
                    '--no-spu',
                    '--no-osd',
                    '--no-stats',
                    '--no-media-library',
                    '--no-playlist-enqueue',
                    '--no-interact'
                ]

            logger.info(f"尝试创建VLC实例，选项: {vlc_options}")
            instance = self.vlc_module.Instance(vlc_options)

            if instance:
                logger.info("VLC实例创建成功")
                # 尝试获取VLC版本信息
                try:
                    version = self.vlc_module.libvlc_get_version().decode('utf-8')
                    logger.info(f"VLC版本: {version}")
                except:
                    logger.warning("无法获取VLC版本信息")

                # 尝试创建一个媒体播放器来进一步测试
                try:
                    player = instance.media_player_new()
                    if player:
                        logger.info("VLC媒体播放器创建成功")
                        player.release()
                    else:
                        logger.warning("无法创建VLC媒体播放器")
                except Exception as e:
                    logger.warning(f"创建媒体播放器失败: {e}")

                instance.release()
                logger.info("VLC功能测试通过")
                return True
            else:
                logger.error("VLC功能测试失败：无法创建实例")
                return False

        except Exception as e:
            logger.error(f"VLC功能测试失败: {e}")
            import traceback
            logger.error(f"详细错误信息: {traceback.format_exc()}")
            return False

# 全局VLC加载器实例
vlc_loader = VLCLoader()

def get_vlc():
    """获取VLC模块"""
    return vlc_loader.load_vlc()

def test_vlc():
    """测试VLC是否可用"""
    try:
        vlc = get_vlc()
        return vlc_loader.test_vlc_functionality()
    except Exception:
        return False
