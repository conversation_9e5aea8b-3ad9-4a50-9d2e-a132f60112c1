#!/usr/bin/env python3
"""
VLC运行时修复工具
用于在打包环境中修复VLC相关问题
"""
import os
import sys
import logging
import shutil
from pathlib import Path

logger = logging.getLogger(__name__)

def fix_vlc_runtime_environment():
    """修复VLC运行时环境"""
    if not hasattr(sys, '_MEIPASS'):
        logger.info("非打包环境，跳过VLC运行时修复")
        return True
    
    base_path = sys._MEIPASS
    # logger.info(f"开始修复VLC运行时环境，基础路径: {base_path}")
    
    try:
        # 1. 创建符号链接
        _create_vlc_symlinks(base_path)
        
        # 2. 设置环境变量
        _setup_vlc_environment(base_path)
        
        # 3. 验证VLC文件
        _verify_vlc_files(base_path)
        
        # logger.info("VLC运行时环境修复完成")
        return True
        
    except Exception as e:
        logger.error(f"VLC运行时环境修复失败: {e}")
        return False

def _create_vlc_symlinks(base_path):
    """创建VLC库文件的符号链接"""
    # logger.info("创建VLC库文件符号链接...")
    
    # VLC库文件映射
    symlink_mappings = [
        ('libvlc.so.5.6.0', 'libvlc.so.5'),
        ('libvlc.so.5.6.0', 'libvlc.so'),
        ('libvlccore.so.9.0.0', 'libvlccore.so.9'),
        ('libvlccore.so.9.0.0', 'libvlccore.so'),
    ]
    
    for source, target in symlink_mappings:
        source_path = os.path.join(base_path, source)
        target_path = os.path.join(base_path, target)
        
        if os.path.exists(source_path) and not os.path.exists(target_path):
            try:
                os.symlink(source, target_path)
                # logger.info(f"创建符号链接: {target} -> {source}")
            except Exception as e:
                logger.warning(f"创建符号链接失败 {target}: {e}")

def _setup_vlc_environment(base_path):
    """设置VLC环境变量"""
    # logger.info("设置VLC环境变量...")
    
    # VLC插件路径
    plugin_paths = [
        os.path.join(base_path, 'vlc', 'plugins'),
        os.path.join(base_path, 'plugins'),
    ]
    
    for plugin_path in plugin_paths:
        if os.path.exists(plugin_path):
            os.environ['VLC_PLUGIN_PATH'] = plugin_path
            # logger.info(f"设置VLC_PLUGIN_PATH: {plugin_path}")
            break
    else:
        logger.warning("未找到VLC插件目录")
    
    # 库文件路径
    if 'LD_LIBRARY_PATH' in os.environ:
        if base_path not in os.environ['LD_LIBRARY_PATH']:
            os.environ['LD_LIBRARY_PATH'] = f"{base_path}:{os.environ['LD_LIBRARY_PATH']}"
    else:
        os.environ['LD_LIBRARY_PATH'] = base_path
    
    # VLC数据路径
    os.environ['VLC_DATA_PATH'] = base_path
    os.environ['VLC_LIB_PATH'] = base_path
    
    # logger.info(f"LD_LIBRARY_PATH: {os.environ.get('LD_LIBRARY_PATH', '')}")

def _verify_vlc_files(base_path):
    """验证VLC文件是否存在"""
    # logger.info("验证VLC文件...")
    
    required_files = [
        'libvlc.so.5.6.0',
        'libvlccore.so.9.0.0'
    ]
    
    missing_files = []
    for file_name in required_files:
        file_path = os.path.join(base_path, file_name)
        if not os.path.exists(file_path):
            missing_files.append(file_name)
        # else:
        #     logger.info(f"找到VLC文件: {file_name}")
    
    if missing_files:
        logger.error(f"缺少VLC文件: {missing_files}")
        return False
    
    # 检查插件目录
    plugin_paths = [
        os.path.join(base_path, 'vlc', 'plugins'),
        os.path.join(base_path, 'plugins'),
    ]
    
    plugin_found = False
    for plugin_path in plugin_paths:
        if os.path.exists(plugin_path):
            plugin_count = len([f for f in os.listdir(plugin_path) if os.path.isdir(os.path.join(plugin_path, f))])
            # logger.info(f"找到VLC插件目录: {plugin_path} (包含 {plugin_count} 个插件类别)")
            plugin_found = True
            break
    
    if not plugin_found:
        logger.warning("未找到VLC插件目录，VLC可能无法正常工作")
    
    return True

def create_vlc_cache():
    """创建VLC插件缓存"""
    if not hasattr(sys, '_MEIPASS'):
        return
    
    base_path = sys._MEIPASS
    plugin_path = os.environ.get('VLC_PLUGIN_PATH')
    
    if not plugin_path or not os.path.exists(plugin_path):
        logger.warning("VLC插件路径不存在，跳过缓存创建")
        return
    
    try:
        # 查找vlc-cache-gen工具
        cache_gen_paths = [
            os.path.join(base_path, 'vlc-cache-gen'),
            '/usr/lib/x86_64-linux-gnu/vlc/vlc-cache-gen',
            'vlc-cache-gen'
        ]
        
        cache_gen = None
        for path in cache_gen_paths:
            if os.path.exists(path) and os.access(path, os.X_OK):
                cache_gen = path
                break
        
        if cache_gen:
            import subprocess
            result = subprocess.run([cache_gen, plugin_path], 
                                  capture_output=True, text=True, timeout=30)
            # if result.returncode == 0:
            #     logger.info("VLC插件缓存创建成功")
            # else:
            #     logger.warning(f"VLC插件缓存创建失败: {result.stderr}")
        else:
            logger.info("未找到vlc-cache-gen工具，跳过缓存创建")
            
    except Exception as e:
        logger.warning(f"创建VLC插件缓存时出错: {e}")

if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    success = fix_vlc_runtime_environment()
    if success:
        create_vlc_cache()
        print("VLC运行时环境修复完成")
    else:
        print("VLC运行时环境修复失败")
        sys.exit(1)
